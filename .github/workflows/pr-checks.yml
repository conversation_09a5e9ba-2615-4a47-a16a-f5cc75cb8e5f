name: PR Checks

on:
  pull_request:
    branches: [main, develop]
  pull_request_target:
    branches: [main, develop]

jobs:
  checks:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: TypeScript check
        run: npm run check-types

      - name: ESLint check
        run: npm run eslint

      - name: Prettier check
        run: npm run prettier

      - name: Run tests
        run: npm run test

      - name: Build shop app
        run: npm run build:shop

      - name: Build GPO portal app
        run: npm run build:gpo

  security:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Check for vulnerabilities
        run: npm audit --audit-level=high --json > audit-results.json || true

      - name: Upload audit results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-audit-results
          path: audit-results.json

  build-verification:
    runs-on: ubuntu-latest
    needs: [checks]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build and verify shop app
        run: |
          npm run build:shop
          echo "Shop app build successful"

      - name: Build and verify GPO portal app
        run: |
          npm run build:gpo
          echo "GPO portal app build successful"

      - name: Verify build artifacts
        run: |
          ls -la build/shop/
          ls -la build/gpo-portal/
          echo "Build artifacts verified"

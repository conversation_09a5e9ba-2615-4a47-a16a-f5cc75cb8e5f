import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { fetchApi } from '@/libs/utils/api';
import { GetDataWithPagination } from '@/types/utility';
import { buildQueryString, createStore } from '@/utils';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { queryClient, queryKeys } from '@/libs/query/queryClient';

import { Actions, SearchState, State } from './types';
import { ProductType, SearchParamsProps } from '@/types';
import { PromoType } from '@/types/common';

const SEARCH_INITIAL_STATE: SearchState = {
  productList: [],
  page: 1,
  total: 0,
  perPage: '12',
  query: '',
  sortOptions: {
    sortOrder: undefined,
    sortBy: undefined,
  },
  filterOptions: {
    vendorIds: '',
  },
};

export const useProductStore = createStore<State & Actions>()(
  immer(
    devtools((set, getState) => ({
      ...SEARCH_INITIAL_STATE,
      fetchProductDetails: async (id?: string) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        const response = await fetchApi<ProductType>(
          `/clinics/${clinicId}/products/${id}`,
        );

        return response;
      },
      fetchProductPromotions: async (productId?: string) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        const response = await fetchApi<PromoType[]>(
          `/clinics/${clinicId}/products/${productId}/promotions`,
        );

        return response;
      },
      updateSearchQueryValue: (query) => set({ query }),
      getSearchProduct: async (
        params: Partial<SearchParamsProps<ProductType>>,
        updateQuery,
      ) => {
        const { sortOptions, query } = getState();
        const clinicId = useClinicStore.getState().clinic?.id;

        const queryParams: SearchParamsProps<ProductType> = {
          query: query.trim(),
          sortBy: sortOptions.sortBy,
          sortOrder: sortOptions.sortOrder,
          clinicId: clinicId || '',
          page: params.page || getState().page || 1,
          perPage: params.perPage || getState().perPage || '12',
          vendorIds:
            params.vendorIds || getState().filterOptions.vendorIds || '',
          ...params,
        };

        if (!queryParams.query) {
          return;
        }

        const { sortBy, sortOrder, page, perPage, vendorIds, ...rest } =
          queryParams;
        let newQuery =
          buildQueryString<Partial<SearchParamsProps<ProductType>>>(rest);

        if (page) newQuery += `&page[number]=${page}`;
        if (perPage) newQuery += `&page[size]=${perPage}`;
        if (vendorIds) newQuery += `&filter[vendorIds]=${vendorIds}`;

        const response = await queryClient.fetchQuery({
          queryKey: clinicId
            ? queryKeys.products.search(clinicId, newQuery)
            : ['products', 'search', 'no-clinic'],
          queryFn: () =>
            fetchApi<GetDataWithPagination<ProductType>>(
              `/clinics/${clinicId}/search?${newQuery}`,
            ),
        });

        // TBD the implementation of sorting by vendor
        // Bubble up first vendor filter to the top of the offers list
        // if (vendorIds?.length) {
        //   response.data.forEach((item) => {
        //     item.offers.sort((offer) =>
        //       offer.vendor.id === vendorIds![0] ? -1 : 1,
        //     );
        //   });
        // }

        set({
          productList: response.data,
          total: response.meta.total,
          sortOptions: {
            sortBy,
            sortOrder,
          },
          filterOptions: {
            vendorIds: vendorIds ?? '',
          },
          page,
          perPage,
          ...rest,
        });

        updateQuery(
          buildQueryString<SearchParamsProps<ProductType>>(queryParams),
        );
      },
      clearSearchProduct: () => set({ ...SEARCH_INITIAL_STATE }),
      addToFavorite: async (productId) => {
        const clinicId = useClinicStore.getState().clinic?.id;
        try {
          await fetchApi<ProductType>(
            `/clinics/${clinicId}/favorite-products`,
            {
              method: 'POST',
              body: {
                productId,
              },
            },
          );

          return true;
        } catch {
          return false;
        }
      },
      removeToFavorite: async (productId) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        try {
          await fetchApi(
            `/clinics/${clinicId}/favorite-products/${productId}`,
            {
              method: 'DELETE',
            },
          );

          return true;
        } catch {
          return false;
        }
      },
      updateProductList: (list) => {
        set({ productList: list });
      },
    })),
  ),
);

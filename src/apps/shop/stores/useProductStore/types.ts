import type { ProductType, SearchParamsProps } from '@/types';
import type { SortOptionsProps } from '@/types/utility';
import type { PromoType } from '@/types/common';

export interface SearchState {
  total: number;
  productList: ProductType[];
  page: number;
  perPage: string;
  query: string;
  sortOptions: Partial<SortOptionsProps<ProductType>>;
  filterOptions: {
    vendorIds: string;
  };
}

export interface ProductLastOrdersType {
  label: string;
  data: number[];
  backgroundColor: string;
}

export type State = SearchState;

export interface Actions {
  fetchProductDetails: (id?: string) => Promise<ProductType>;
  fetchProductPromotions: (productId?: string) => Promise<PromoType[]>;
  getSearchProduct: (
    params: Partial<SearchParamsProps<ProductType>>,
    updateQuery: (value: string) => void,
  ) => void;
  updateSearchQueryValue: (value: string) => void;
  clearSearchProduct: () => void;
  addToFavorite: (productId: string) => Promise<boolean>;
  removeToFavorite: (productId: string) => Promise<boolean>;
  updateProductList: (list: ProductType[]) => void;
}

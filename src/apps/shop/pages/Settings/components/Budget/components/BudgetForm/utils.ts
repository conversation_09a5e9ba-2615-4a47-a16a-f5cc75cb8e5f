import { FormValues } from '../../interfaces';
import { BudgetType } from '@/libs/cart/types';

const clearNumberValue = (value: number | string) => {
  const integerValue = +value?.toString()?.replace(/,/g, '');
  return integerValue ? integerValue : 0;
};

export function transformValueToSend({
  avgTwoWeeksSales,
  includeExternalData,
  monthToDateSales,
  targetCogsPercent,
  targetGaPercent,
  monthlyCogs,
  externalMonthlyCogs,
  monthlyGa,
  externalWeeklyCogs,
  weeklyCogs,
  weeklyGa,
  type,
}: FormValues): BudgetType {
  return {
    type,
    avgTwoWeeksSales: clearNumberValue(avgTwoWeeksSales),
    monthToDateSales: clearNumberValue(monthToDateSales),
    monthlyCogs: clearNumberValue(monthlyCogs),
    monthlyGa: clearNumberValue(monthlyGa),
    targetCogsPercent: targetCogsPercent
      ? +(targetCogsPercent / 100).toFixed(2)
      : 0,
    targetGaPercent: targetGaPercent ? +(targetGaPercent / 100).toFixed(2) : 0,
    weeklyCogs: clearNumberValue(weeklyCogs),
    weeklyGa: clearNumberValue(weeklyGa),
    includeExternalData: includeExternalData,
    externalMonthlyCogs: clearNumberValue(externalMonthlyCogs),
    externalWeeklyCogs: clearNumberValue(externalWeeklyCogs),
  };
}

export function transformValueToForm({
  avgTwoWeeksSales,
  includeExternalData,
  monthToDateSales,
  targetCogsPercent,
  targetGaPercent,
  monthlyCogs,
  externalMonthlyCogs,
  monthlyGa,
  externalWeeklyCogs,
  weeklyCogs,
  weeklyGa,
  type,
}: BudgetType): FormValues {
  return {
    type,
    avgTwoWeeksSales: avgTwoWeeksSales,
    monthToDateSales: monthToDateSales,
    monthlyCogs: monthlyCogs,
    monthlyGa: monthlyGa,
    targetCogsPercent: targetCogsPercent
      ? +(targetCogsPercent * 100).toFixed(2)
      : 0,
    targetGaPercent: targetGaPercent ? +(targetGaPercent * 100).toFixed(2) : 0,
    weeklyCogs: weeklyCogs,
    weeklyGa: weeklyGa,
    includeExternalData,
    externalMonthlyCogs: externalMonthlyCogs,
    externalWeeklyCogs: externalWeeklyCogs,
  };
}

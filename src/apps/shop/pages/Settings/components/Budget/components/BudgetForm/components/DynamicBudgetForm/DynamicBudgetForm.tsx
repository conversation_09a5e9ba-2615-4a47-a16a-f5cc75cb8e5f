import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Resolver } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BudgetType } from '@/libs/cart/types';
import { successNotification } from '@/utils';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Input } from '@/libs/form/Input';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import { moneyMask } from '@/libs/form/masks';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { DynamicFormValues, FormValues } from '../../../../interfaces';
import { DYNAMIC_SCHEMA } from '../../constants';
import { transformValueToForm, transformValueToSend } from '../../utils';

interface DynamicBudgetFormProps {
  onUpdate: (formData: FormValues) => Promise<void>;
  budget: BudgetType | null;
}

export const DynamicBudgetForm = ({
  onUpdate,
  budget,
}: DynamicBudgetFormProps) => {
  const { t } = useTranslation();
  const [includeExternalData, setIncludeExternalData] = useState(false);

  const getInitialValues = useCallback(() => {
    if (budget && budget.type) {
      const transformedValues = transformValueToForm(budget);
      return {
        targetCogsPercent: transformedValues.targetCogsPercent,
        targetGaPercent: transformedValues.targetGaPercent,
        avgTwoWeeksSales: transformedValues.avgTwoWeeksSales,
        monthToDateSales: transformedValues.monthToDateSales,
        externalWeeklyCogs: transformedValues.externalWeeklyCogs,
        externalMonthlyCogs: transformedValues.externalMonthlyCogs,
      };
    } else {
      return {
        targetCogsPercent: 0,
        targetGaPercent: 0,
        avgTwoWeeksSales: 0,
        monthToDateSales: 0,
        externalWeeklyCogs: 0,
        externalMonthlyCogs: 0,
      };
    }
  }, [budget]);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm<DynamicFormValues>({
    resolver: yupResolver(
      DYNAMIC_SCHEMA,
    ) as unknown as Resolver<DynamicFormValues>,
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: getInitialValues(),
  });

  useEffect(() => {
    if (budget) {
      setIncludeExternalData(budget.includeExternalData);
      reset(getInitialValues());
    }
  }, [budget, getInitialValues, reset]);

  const onSubmit = async (values: DynamicFormValues) => {
    try {
      const formData = transformValueToSend({
        ...values,
        includeExternalData,
        type: 'DYNAMIC',
      } as FormValues);

      await onUpdate(formData);

      successNotification(
        t('client.settings.updatedSettings', {
          tab: t('client.settings.budget.title'),
        }),
      );
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <form id="dynamic-form" onSubmit={handleSubmit(onSubmit)}>
      <Flex gap="1rem" mb="24px">
        <div className="flex-1">
          <Input
            label={
              <>
                {t('client.settings.budget.COGSTarget')}
                {' % '}
                <HelpTooltip message={t('client.settings.budget.COGSHelp')} />
              </>
            }
            {...register('targetCogsPercent')}
            error={errors.targetCogsPercent?.message}
          />
        </div>
        <div className="flex-1">
          <Input
            label={
              <>
                {t('client.settings.budget.GATarget')}
                {' % '}
                <HelpTooltip message={t('client.settings.budget.GAHelp')} />
              </>
            }
            {...register('targetGaPercent')}
            error={errors.targetGaPercent?.message}
          />
        </div>
      </Flex>
      <Flex gap="1rem" mb="24px">
        <div className="flex-1">
          <Input
            mask={moneyMask}
            label={t('client.settings.budget.avgTwoWeeksSales')}
            {...register('avgTwoWeeksSales')}
            error={errors.avgTwoWeeksSales?.message}
          />
        </div>
        <div className="flex-1">
          <Input
            mask={moneyMask}
            label={t('client.settings.budget.monthToDateSales')}
            {...register('monthToDateSales')}
            error={errors.monthToDateSales?.message}
          />
        </div>
      </Flex>

      <Flex
        gap="1rem"
        my="24px"
        style={{ display: includeExternalData ? 'flex' : 'none' }}
      >
        <div className="flex-1">
          <Input
            mask={moneyMask}
            label={t('client.settings.budget.externalWeeklyCogs')}
            {...register('externalWeeklyCogs')}
            error={errors.externalWeeklyCogs?.message}
          />
        </div>
        <div className="flex-1">
          <Input
            mask={moneyMask}
            label={t('client.settings.budget.externalMonthlyCogs')}
            {...register('externalMonthlyCogs')}
            error={errors.externalMonthlyCogs?.message}
          />
        </div>
      </Flex>

      <div className="mb-6">
        <Checkbox
          label={t('client.settings.budget.costsInCOGS')}
          checked={includeExternalData}
          onChange={() => {
            setIncludeExternalData(!includeExternalData);
          }}
        />
      </div>

      <Button form="dynamic-form" variant="secondary" disabled={!isValid}>
        {t('common.save')}
      </Button>
    </form>
  );
};

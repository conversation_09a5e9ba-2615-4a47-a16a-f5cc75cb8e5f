import { ShippingStatusType } from '@/libs/orders/types';
import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { ORDER_STATUS_CONFIGS } from './constants';
import styles from './OrderStatus.module.css';
import { Icon } from '@/libs/icons/Icon';

interface StepProps {
  completed: boolean;
  hasTail: boolean;
  completedColor: string;
}

const Step = ({ completed, hasTail, completedColor }: StepProps) => {
  const color = completed ? completedColor : '#D2D2D2';

  return (
    <>
      {hasTail && (
        <div className="h-0.5 w-3" style={{ backgroundColor: color }} />
      )}
      <div
        className="h-2 w-2 rounded-full"
        style={{ backgroundColor: color }}
      />
    </>
  );
};

interface OrderStatusProps {
  status: ShippingStatusType;
  align?: 'start' | 'center' | 'end';
  showStepProgress?: boolean;
  trackLink?: string;
}

export const OrderStatus = ({
  status,
  align = 'start',
  showStepProgress = false,
  trackLink,
}: OrderStatusProps) => {
  const statusConfig = ORDER_STATUS_CONFIGS[status];

  if (!statusConfig) {
    return null;
  }

  const { color, Icon: StatusIcon, label, step } = statusConfig;

  const statusSteps = Array(5)
    .fill(false)
    .map((_, index) => (
      <Step
        key={+index}
        completedColor={color}
        hasTail={index > 0}
        completed={Boolean(step && index < step)}
      />
    ));

  const Wrapper = trackLink ? 'a' : 'div';

  return (
    <div>
      <Wrapper
        className={`flex items-center justify-${align} gap-[0.375rem]`}
        href={trackLink}
        target="_blank"
        rel="noreferrer"
      >
        {StatusIcon ? <StatusIcon className={styles.icon} /> : null}
        <Text c={color} size="xs" fw="bold" lh={1}>
          {label}
        </Text>

        {trackLink ? (
          <Icon name="arrowUp" color={color} className="w-3 rotate-90" />
        ) : null}
      </Wrapper>
      {showStepProgress && step && (
        <Flex mt="0.25rem" align="center" justify={align}>
          {statusSteps}
        </Flex>
      )}
    </div>
  );
};

import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { ORDER_STATUS_CONFIGS } from './constants';
import { ShippingStatusType } from '@/libs/orders/types';
import { OrderStatus } from './OrderStatus';

type Story = StoryObj<typeof OrderStatus>;

const Wrap = () => {
  return (
    <Flex gap="xl">
      <div className="p-6">
        <Text fw="bold" mb="lg">
          Align: Left (Default)
        </Text>
        {Object.keys(ORDER_STATUS_CONFIGS).map((status) => (
          <div className="mb-4" key={status}>
            <OrderStatus
              status={status as unknown as ShippingStatusType}
              align="start"
              showStepProgress
            />
          </div>
        ))}
      </div>
      <div className="p-6">
        <Text fw="bold" mb="lg" ta="center">
          Align: Center
        </Text>
        {Object.keys(ORDER_STATUS_CONFIGS).map((status) => (
          <div className="mb-4" key={status}>
            <OrderStatus
              status={status as unknown as ShippingStatusType}
              align="center"
              showStepProgress
            />
          </div>
        ))}
      </div>
      <div className="p-6">
        <Text fw="bold" mb="lg" ta="right">
          Align: Right
        </Text>
        {Object.keys(ORDER_STATUS_CONFIGS).map((status) => (
          <div className="mb-4" key={status}>
            <OrderStatus
              status={status as unknown as ShippingStatusType}
              align="end"
              showStepProgress
            />
          </div>
        ))}
      </div>
    </Flex>
  );
};

const meta: Meta<typeof OrderStatus> = {
  title: 'Product/OrderStatus',
  component: Wrap,
};
export default meta;

export const Default: Story = {
  args: {},
};

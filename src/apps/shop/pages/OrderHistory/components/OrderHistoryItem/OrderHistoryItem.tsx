import dayjs from 'dayjs';
import clsx from 'clsx';
import { OrderHistoryItemType } from '@/libs/orders/types';
import styles from './OrderHistoryItem.module.css';
import { Link } from 'react-router-dom';
import { getOrderHistoryItemUrl } from '@/apps/shop/routes/utils';
import { DEFAULT_DISPLAY_DATE_FORMAT, FEATURE_FLAGS } from '@/constants';
import { getPriceString } from '@/utils';
import { OrderStatus } from '../OrderStatus/OrderStatus';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';

interface OrderHistoryItemProps {
  order: OrderHistoryItemType;
  isActive: boolean;
}

export const OrderHistoryItem = ({
  order,
  isActive,
}: OrderHistoryItemProps) => {
  const {
    backorderedItemsCount,
    id,
    date,
    itemsCount,
    orderNumber,
    status,
    totalPrice,
    vendorsCount,
  } = order;
  return (
    <Link
      to={getOrderHistoryItemUrl(id)}
      className={clsx(styles.container, { [styles.active]: isActive })}
    >
      <div className="px-8 py-4">
        <div className="flex justify-between">
          <div>
            <p className="text-xs text-black/60">Order ID</p>
            <p className="text-lg font-medium">{orderNumber}</p>
          </div>
          <div>
            <p className="text-right text-xs text-black/60">Order Total</p>
            <p className="text-right text-lg font-medium">
              {getPriceString(totalPrice)}
            </p>
          </div>
        </div>
        <div className="divider-h mt-3 mb-4"></div>
        <div className="flex justify-between gap-16">
          <div className="flex flex-col text-nowrap">
            <span className="mr-1 mb-1 text-xs font-bold">
              <span className="text-xs font-normal text-black/60">Date:</span>
              {' ' + dayjs(date).format(DEFAULT_DISPLAY_DATE_FORMAT)}
            </span>
            <span className="mr-1 mb-1 text-xs font-bold">
              <span className="text-xs font-normal text-black/60">
                Total Line Items:
              </span>
              {' ' + itemsCount}
            </span>
            <span className="mr-1 mb-1 text-xs font-bold">
              <span className="text-xs font-normal text-black/60">
                Total vendors:
              </span>
              {' ' + vendorsCount}
            </span>
            {!!backorderedItemsCount && (
              <span className="mr-1 mb-1 flex items-center gap-1 text-xs font-bold">
                <span className="text-xs font-normal text-black/60">
                  Backordered items:
                </span>
                {' ' + backorderedItemsCount}
                <Icon name="alert" color="#F14336" size="0.7rem" />
              </span>
            )}
          </div>
          <div className="flex flex-col items-end">
            <OrderStatus status={status} align="end" showStepProgress />
            {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
              <>
                <Button variant="unstyled" className="mt-1.5">
                  <p className="text-xs underline">See Details</p>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
};

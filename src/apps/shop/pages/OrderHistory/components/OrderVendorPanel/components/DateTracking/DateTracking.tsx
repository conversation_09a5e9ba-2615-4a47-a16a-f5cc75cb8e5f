import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import dayjs from 'dayjs';
import type {
  OrderHistoryDetailItemType,
  OrderShipment,
} from '@/libs/orders/types';

interface DateTrackingProps {
  shipment: OrderShipment;
  item: OrderHistoryDetailItemType;
  dateDelivered?: string | null;
  etaDate?: string | null;
}

export const DateTracking = ({
  shipment,
  item,
  dateDelivered,
}: DateTrackingProps) => {
  const etaDate = [
    'PENDING',
    'PROCESSING',
    'PARTIALLY_SHIPPED',
    'ACCEPTED',
    'SHIPPED',
  ].includes(item.status)
    ? shipment?.etaDateTo || shipment?.etaDateFrom || null
    : null;

  if (etaDate) {
    return (
      <p className="text-sxs flex gap-1 text-xs whitespace-nowrap text-gray-500">
        Expected Delivery:
        <span className="font-bold text-gray-700">
          {dayjs(etaDate).format(DEFAULT_DISPLAY_DATE_FORMAT)}
        </span>
      </p>
    );
  }

  if (dateDelivered) {
    return (
      <div className="text-sxs flex flex-col gap-1">
        <p className="text-xs text-gray-500">
          {dayjs(dateDelivered).format(DEFAULT_DISPLAY_DATE_FORMAT)}
        </p>
      </div>
    );
  }

  if (item.product.isControlled222Form) {
    return (
      <div className="flex flex-col gap-1">
        <div className="flex items-center justify-end gap-1">
          <HelpTooltip
            message="Typical delivery is 5–7 business days after the vendor receives the 222 form."
            color="grey"
            size="0.6rem"
          />
          <p className="text-sxs flex gap-1 whitespace-nowrap text-gray-500">
            Expected Delivery:
            <span className="font-bold text-gray-700">5-7 business days</span>
          </p>
        </div>
      </div>
    );
  }

  return null;
};

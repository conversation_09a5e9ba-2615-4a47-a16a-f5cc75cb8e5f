import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Image } from '@mantine/core';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import type {
  OrderHistoryDetailItemType,
  OrderHistoryDetailVendorOrderType,
  OrderHistoryPromotion,
  ShippingStatusType,
} from '@/libs/orders/types';
import { groupByPromotions } from '../../utils/promotion/groupByPromotions';
import { OrderHistoryBuyGetPromotionItem } from './components/OrderHistoryBuyGetPromotionItem/OrderHistoryBuyGetPromotionItem';
import { ORDER_STATUS_CONFIGS } from '../OrderStatus/constants';
import { Badge } from '@/libs/ui/Badge/Badge';
import { OrderHistoryOfferItem } from './components/OrderHistoryOfferItem/OrderHistoryOfferItem';

interface OrderVendorPanelProps {
  vendor: OrderHistoryDetailVendorOrderType['vendor'];
  items: OrderHistoryDetailItemType[];
  totalPrice: number;
  promotions: OrderHistoryPromotion[] | null;
}

const byBackorderedStatus = ({ status }: { status: ShippingStatusType }) => {
  return status === ORDER_STATUS_CONFIGS.BACKORDERED.label.toLocaleUpperCase();
};

export const OrderVendorPanel = ({
  vendor,
  totalPrice,
  items,
  promotions = [],
}: OrderVendorPanelProps) => {
  const groupedItems = groupByPromotions({
    items,
    promotions: promotions ?? [],
  });

  let totalItems = groupedItems.untriggeredItems?.length;
  totalItems +=
    groupedItems.buy_x_get_y?.reduce(
      (acc, promotionGroup) => acc + (promotionGroup.items.length || 0),
      0,
    ) || 0;

  const backorderedItemsCount = items.filter(byBackorderedStatus).length;

  return (
    <CollapsiblePanel
      startOpen
      header={
        <div className="flex w-full items-center pr-20">
          <Image
            src={vendor.imageUrl}
            alt={vendor.name}
            fallbackSrc={defaultProductImgUrl}
            h={42}
          />
          <div className="flex w-full items-center justify-between">
            <div className="ml-4">
              <p className="text-md font-medium">
                {getPriceString(totalPrice)}
                <span className="ml-1 text-xs text-black/60">
                  ({totalItems} Items)
                </span>
              </p>
            </div>
            {!!backorderedItemsCount && (
              <div className="mr-4">
                <Badge className="h-[27px] bg-[#FAD6CF] text-xs text-red-800">
                  Backordered items {`(${backorderedItemsCount})`}
                </Badge>
              </div>
            )}
          </div>
        </div>
      }
      content={
        <div className="flex flex-col gap-4 divide-y divide-gray-200/80 px-4 pt-6 pb-2">
          {groupedItems.buy_x_get_y &&
            groupedItems.buy_x_get_y.map((promotionGroup) => (
              <OrderHistoryBuyGetPromotionItem
                key={promotionGroup.items[0].id}
                promotion={promotionGroup.promotion}
                items={promotionGroup.items}
              />
            ))}
          {groupedItems.untriggeredItems.map((item) => (
            <OrderHistoryOfferItem key={item.id} item={item} />
          ))}
        </div>
      }
    />
  );
};

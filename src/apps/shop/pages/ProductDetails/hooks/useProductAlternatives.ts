import { useQuery } from '@tanstack/react-query';
import { fetchApi } from '@/libs/utils/api';
import { queryKeys } from '@/libs/query/queryClient';
import {
  ProductAlternativesResponse,
  UseProductAlternativesParams,
} from '../types';

export const useProductAlternatives = ({
  productId,
  clinicId,
}: UseProductAlternativesParams) => {
  return useQuery({
    queryKey: queryKeys.products.alternatives(productId, clinicId),
    queryFn: async (): Promise<ProductAlternativesResponse> => {
      const response = await fetchApi<ProductAlternativesResponse>(
        `/clinics/${clinicId}/products/${productId}/alternatives`,
      );
      return response;
    },
    enabled: !!productId && !!clinicId,
  });
};

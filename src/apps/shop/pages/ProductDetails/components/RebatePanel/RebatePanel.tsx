import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { PromoType } from '@/types/common';

interface RebatePanelProps {
  promotions?: PromoType[];
  selectedOfferVendorId?: string;
}
export const RebatePanel = ({
  promotions = [],
  selectedOfferVendorId,
}: RebatePanelProps) => {
  // Filter for rebate type promotions that match the selected offer's vendor
  const rebatePromotions = promotions.filter(
    (promo) =>
      promo.type === 'rebate' && promo.vendor.id === selectedOfferVendorId,
  );

  return rebatePromotions.length > 0 ? (
    <div className="mb-8">
      <CollapsiblePanel
        header={
          <Flex h="100%" align="center" ml="1.5rem">
            <Text size="md" fw={500}>
              Rebates
            </Text>
          </Flex>
        }
        content={
          <div className="mb-6 p-4">
            {rebatePromotions.map(({ id, name, description }, index) => (
              <div key={id}>
                {index !== 0 ? <Divider my="md" /> : null}
                <Text fw="500" mb="sm">
                  {name}
                </Text>
                <MarkdownRenderer markdown={description} />
              </div>
            ))}
          </div>
        }
        startOpen
      />
    </div>
  ) : null;
};

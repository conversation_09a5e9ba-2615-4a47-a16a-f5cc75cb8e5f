import { Tooltip } from '@/libs/ui/Tooltip';
import { getPriceString, mergeClasses } from '@/utils';
import { MODAL_NAME } from '@/constants';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import type { CartItemType } from '@/libs/cart/types';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { useAddToSavedItems } from '@/libs/cart/hooks/useAddToSavedItems';

export const CartVendorProductActions = ({
  cartItem,
  className,
}: {
  cartItem: CartItemType;
  className?: string;
}) => {
  const { id, product, subtotal, productOfferId } = cartItem;
  const offer = product.offers.find(({ id }) => productOfferId === id);

  const { openModal } = useModalStore();
  const { addToCart, offersMapData } = useCartStore();
  const { mutate: addToSavedItems } = useAddToSavedItems();
  const { quantity } = offersMapData[offer!.id]!;

  const handleOpenRemoveProductModal = () => {
    openModal({
      name: MODAL_NAME.REMOVE_PRODUCT_FROM_CART,
      offerIds: [productOfferId],
      itemName: product?.name,
    });
  };

  if (!offer) {
    return null;
  }

  const subtotalWithoutDiscount = +offer.price! * (cartItem.quantity || 1);

  return (
    <div className={mergeClasses('flex flex-col', className)}>
      {quantity && (
        <>
          <div className="flex">
            <span className="mb-2 w-full text-right text-xs font-medium whitespace-nowrap text-[rgba(102,102,102,0.80)]">
              Unit Price:{' '}
              <span className="text-xs font-medium text-[#333]">
                {getPriceString(offer.clinicPrice || offer.price)}
              </span>
            </span>
            <Icon name="multiply" />
          </div>
          <AddToCartInput
            key={quantity}
            originalAmount={quantity}
            minIncrement={offer.increments}
            onUpdate={({ amount: newQuantity, setError }) =>
              addToCart({
                offers: [
                  {
                    productOfferId,
                    quantity: newQuantity,
                  },
                ],
                onError: (message) => {
                  setError(message);
                },
              })
            }
          />
        </>
      )}

      <div className="mt-2 mb-3 flex items-end justify-end gap-2.5">
        {subtotal && subtotalWithoutDiscount > +subtotal ? (
          <span className="text-sm font-medium text-[rgba(51,51,51,0.50)] line-through">
            {getPriceString(subtotalWithoutDiscount)}
          </span>
        ) : null}
        <span className="text-2xl font-medium">{getPriceString(subtotal)}</span>
      </div>
      <div className="flex items-center justify-end gap-4">
        <Tooltip label="Save item for later">
          <Button
            variant="unstyled"
            aria-label="Save item for later"
            onClick={() => addToSavedItems([id])}
          >
            <Icon
              name="saveFlag"
              color="#667085"
              size="1rem"
              className="transition-colors hover:text-[#447bfd]"
              aria-hidden={true}
            />
          </Button>
        </Tooltip>
        <div className="h-4 w-px bg-[#D0D5DD]" />
        <Tooltip label="Remove item from cart">
          <Button
            onClick={handleOpenRemoveProductModal}
            variant="unstyled"
            aria-label="Remove item from cart"
          >
            <Icon name="trash" color="#667085" size="1rem" aria-hidden={true} />
          </Button>
        </Tooltip>
      </div>
    </div>
  );
};

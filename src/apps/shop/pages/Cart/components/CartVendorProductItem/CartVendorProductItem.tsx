import type { CartItemType } from '@/libs/cart/types';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';
import { CartVendorProductActions } from './components/CartVendorProductActions/CartVendorProductActions';
import { CartVendorProductInfo } from './components/CartVendorProductInfo/CartVendorProductInfo';

export const CartVendorProductItem = ({
  cartItem,
}: {
  cartItem: CartItemType;
}) => {
  return (
    <div className="relative grid w-full grid-cols-[160px_1fr_150px] gap-6 pb-4">
      <ProductImage
        product={{
          ...cartItem.product,
          productOfferId: cartItem.productOfferId,
        }}
      />
      <CartVendorProductInfo cartItem={cartItem} />
      <CartVendorProductActions cartItem={cartItem} />
    </div>
  );
};

import { Progress, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';

import { getPriceString } from '@/utils';
import { Tooltip } from '@/libs/ui/Tooltip';
import QuestionIcon from '@/assets/images/question.svg?react';

import { getPercentage, getProgressColor } from './utils';
import styles from './budget-line.module.css';

interface BudgetLineProps {
  name: string;
  value?: number;
  maxValue?: number;
  isDynamicBudget?: boolean;
  budgetUsed?: string;
  desc: string;
  budgetUsedType: '%' | '$';
}

const PRICE_CONFIG = {
  formatter: new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0,
  }),
};

export const BudgetLine = (props: BudgetLineProps) => {
  const {
    value,
    maxValue,
    name,
    isDynamicBudget,
    budgetUsed,
    desc,
    budgetUsedType,
  } = props;

  const { t } = useTranslation();

  const progress = getPercentage(value, maxValue) || 0;

  const colors = getProgressColor(progress);

  return (
    <div style={colors} className={styles.box}>
      <div className={styles.topLine}>
        <Tooltip label={desc}>
          <QuestionIcon />
        </Tooltip>

        <Text size="xs" c="black">
          {isDynamicBudget ? budgetUsedType : null}
          {budgetUsed}
          {isDynamicBudget ? null : budgetUsedType}

          {t('client.cart.budgetUsed')}
        </Text>
      </div>

      <Progress.Root className={styles.root} size={6} radius="lg">
        <Progress.Section className={styles.section} value={progress || 0} />
      </Progress.Root>

      <div className={styles.text}>
        <Text size="xs" c="black">
          {name}{' '}
          {isDynamicBudget ? `${value}%` : getPriceString(value, PRICE_CONFIG)}
        </Text>

        <Text size="xs" c="black" span>
          {isDynamicBudget
            ? `${maxValue}%`
            : getPriceString(maxValue, PRICE_CONFIG)}{' '}
          {t('client.cart.budgetTarget')}
        </Text>
      </div>
    </div>
  );
};

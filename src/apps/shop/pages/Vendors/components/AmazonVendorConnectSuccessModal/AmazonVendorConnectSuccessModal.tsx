import { Modal } from '@/components';
import styles from './AmazonVendorConnectSuccessModal.module.css';
import { MODAL_NAME } from '@/constants';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Button } from '@/libs/ui/Button/Button';
import { useModalStore } from '@/apps/shop/stores/useModalStore';

export const AmazonVendorConnectSuccessModal = () => {
  const { closeModal } = useModalStore();

  const closeSuccessModal = () => {
    window.history.replaceState({}, document.title, '/vendors');
    closeModal();
  };

  return (
    <Modal
      name={MODAL_NAME.AMAZON_VENDOR_CONNECT_SUCCESS}
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
      onClose={closeSuccessModal}
      closeOnClickOutside={false}
      closeOnEscape={false}
    >
      <div className="relative top-[-1.5rem] w-full">
        <div className="flex w-full flex-col items-center">
          <Logo type="emblem" className="mb-6 w-30" />
          <p className="mb-2 text-center text-xl font-semibold">
            You’re successfully connected with your vendor
          </p>
          <p className="px-2 text-center text-sm text-black/60">
            Now you can take advantage of all the benefits from this
            partnership.
          </p>
          <div className="mt-4 mb-5 flex w-full flex-col items-center rounded-lg border border-black/[0.04] bg-[#F2F8FC] p-4">
            <p className="text-lg font-semibold">You got 50% off</p>
            <p className="text-sm text-black/70">Amazon Business Prime</p>
            <p className="mt-2 mb-8 text-center text-xs text-black/60">
              Use this discount on your purchases through our platform. Don’t
              wait — start saving today!
            </p>

            <Button variant="white" onClick={closeSuccessModal}>
              Thanks
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

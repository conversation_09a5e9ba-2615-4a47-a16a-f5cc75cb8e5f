import {
  ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import styles from './ShippingFeeModal.module.css';
import { Input } from '@/libs/form/Input';
import { VendorType } from '@/types';
import { Button } from '@/libs/ui/Button/Button';
import { Select } from '@/libs/form/Select';
import { moneyMask } from '@/libs/form/masks';
import { shippingOptions, SHIPPING_OPTIONS_VALUES } from './constants';
import { useState } from 'react';

type ShippingFeeModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export const ShippingFeeModal = () => {
  const { closeModal, modalOption } = useModalStore();
  const [selectedOption, setSelectedOption] = useState(
    SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM,
  );

  const { vendor } = modalOption as ShippingFeeModalOptions;

  if (!vendor) {
    return null;
  }

  const showFreeShippingThreshold =
    selectedOption === SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM;
  const showInputs = selectedOption !== SHIPPING_OPTIONS_VALUES.FREE_ALWAYS;

  return (
    <Modal
      name={MODAL_NAME.SHIPPING_FEE}
      size="500px"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
      onClose={closeModal}
      closeOnClickOutside={false}
      closeOnEscape={false}
    >
      <h4 className="mb-2 text-[20px] font-bold text-[#333]">
        Vendor Shipping Information
      </h4>
      <p className="mb-4 text-[14px] text-[#666]">
        Enter your clinic-specific shipping rules for this vendor. Once saved,
        these will override the national shipping defaults and apply to all user
        carts.
      </p>

      <div className="rounded-xl border border-[#F5F5F5] bg-[#F2F8FC] px-4 py-6">
        <div className="flex gap-5">
          <div>
            <img src={vendor.imageUrl} alt={vendor.name} />
          </div>
          <div className="font-medium text-[#333]">
            <h3 className="mb-2 text-[18px]">{vendor.name}</h3>
            <p className="flex gap-1 text-[14px] font-medium capitalize">
              <span className="font-medium text-[#666]">Category:</span>
              {vendor.type}
            </p>
          </div>
        </div>
        <div className="divider-h my-4 w-full" />
        <Select
          label="Shipping Fee Type"
          options={shippingOptions}
          showEmptyOption={false}
          onChange={(event) => setSelectedOption(event.target.value)}
        />
        {showInputs && (
          <div className="mt-4 flex gap-4">
            <Input label="Default Shipping Fee" mask={moneyMask} />
            {showFreeShippingThreshold && (
              <Input label="Free Shipping Threshold ($)" mask={moneyMask} />
            )}
          </div>
        )}
        <Button variant="secondary" className="my-6">
          Save Shipping Information
        </Button>
        <Button
          className="align-center w-full underline"
          variant="unstyled"
          onClick={closeModal}
        >
          Cancel
        </Button>
      </div>
    </Modal>
  );
};

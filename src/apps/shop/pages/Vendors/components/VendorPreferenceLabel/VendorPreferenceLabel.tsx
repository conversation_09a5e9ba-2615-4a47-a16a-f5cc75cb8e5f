import { VendorType } from '@/types';
import { ClinicInfoType } from '../../../Settings/types';

export const VendorPreferenceLabel = ({
  clinicInfo,
  vendor,
}: {
  clinicInfo: ClinicInfoType | null;
  vendor: VendorType;
}) => {
  if (!clinicInfo) return null;
  const { primaryDistributor, secondaryDistributor, preferredManufacturers } =
    clinicInfo;

  const isPrimaryDistributor = primaryDistributor?.id === vendor.id;
  const isSecondaryDistributor = secondaryDistributor?.id === vendor.id;
  const isPreferredManufacturer = preferredManufacturers?.some(
    (manufacturer) => manufacturer.id === vendor.id,
  );

  return (
    <p className="text-sxs font-semibold text-[#3646AC]">
      {isPrimaryDistributor
        ? 'Primary Distributor'
        : isSecondaryDistributor
          ? 'Secondary Distributor'
          : isPreferredManufacturer
            ? 'Preferred Manufacturer'
            : null}
    </p>
  );
};

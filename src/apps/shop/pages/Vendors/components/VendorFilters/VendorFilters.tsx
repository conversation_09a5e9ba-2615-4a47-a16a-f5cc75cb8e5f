import { Tabs } from '@/libs/ui/Tabs/Tabs';

export type VendorFilterType = 'all' | 'preferred' | 'disconnected';

type VendorFiltersProps = {
  activeFilter: VendorFilterType;
  onFilterChange: (filter: VendorFilterType) => void;
  counts: {
    all: number;
    preferred: number;
    disconnected: number;
  };
};

export const VendorFilters = ({
  activeFilter,
  onFilterChange,
  counts,
}: VendorFiltersProps) => {
  const filterOptions: Array<{
    key: VendorFilterType;
    label: string;
    count: number;
  }> = [
    { key: 'all', label: 'All Vendors', count: counts.all },
    { key: 'preferred', label: 'Preferred Vendors', count: counts.preferred },
    {
      key: 'disconnected',
      label: 'Not Connected',
      count: counts.disconnected,
    },
  ];

  const activeIndex = filterOptions.findIndex(
    (option) => option.key === activeFilter,
  );

  const tabs = filterOptions.map((option) => ({
    label: `${option.label} (${option.count})`,
    onClick: () => onFilterChange(option.key),
  }));

  return (
    <div className="text-sm">
      <Tabs active={activeIndex} tabs={tabs} />
    </div>
  );
};

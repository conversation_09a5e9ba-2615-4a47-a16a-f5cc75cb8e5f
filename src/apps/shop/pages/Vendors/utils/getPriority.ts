export const getPriority = (
  isPreferred: boolean,
  isConnected: boolean,
  wasOnceConnected: boolean = false,
) => {
  if (isPreferred && isConnected) return 1;
  if (!isPreferred && isConnected) return 2;
  if (isPreferred && !isConnected && wasOnceConnected) return 3;
  if (isPreferred && !isConnected && !wasOnceConnected) return 4;
  if (!isPreferred && !isConnected && wasOnceConnected) return 5;
  return 6; // !isPreferred && !isConnected && !wasOnceConnected
};

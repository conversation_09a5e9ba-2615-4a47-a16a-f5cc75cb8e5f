import React from 'react';
import { ReportPanel } from '../ReportPanel/ReportPanel';
import { ProgressBar } from '@/libs/ui/ProgressBar/ProgressBar';
import { getPriceString } from '@/utils';

interface SpendPanelProps {
  spendPercentage: string;
  spendAmount: string;
  yoyPercentage: number;
  // Additional dynamic data
  totalSpend: string;
  annualBudget: string;
  prevYearSpend: string;
  preferredVendorsPercentage: number;
  nonPreferredVendorsPercentage: number;
}

export const SpendPanel = ({
  spendPercentage,
  spendAmount,
  yoyPercentage,
  totalSpend,
  annualBudget,
  prevYearSpend,
  preferredVendorsPercentage,
  nonPreferredVendorsPercentage,
}: SpendPanelProps) => {
  return (
    <ReportPanel
      title="Spend"
      label="% Preferred Vendor Spend (YOY)"
      value={spendPercentage}
      subtitle={`(${spendAmount})`}
      percentage={yoyPercentage}
    >
      <>
        <div className="flex gap-5 px-4 py-3">
          <div className="flex w-[170px] flex-col gap-3">
            <div>
              <p className="text-xs text-[#858585]">Total spend:</p>
              <p className="mt-3 font-medium text-[#344054]">
                {getPriceString(totalSpend)}
              </p>
            </div>
            <div className="mx-auto w-full border-t border-[#E4E7EC]" />
            <div>
              <p className="text-sxs mb-2 text-[#858585]">
                Annual Budget:{' '}
                <span className="text-xs font-medium text-[#344054]">
                  {getPriceString(annualBudget)}
                </span>
              </p>
              <p className="text-sxs text-[#858585]">
                Prev year spend:{' '}
                <span className="text-xs font-medium text-[#344054]">
                  {getPriceString(prevYearSpend)}
                </span>
              </p>
            </div>
          </div>
          <div className="flex-1 rounded bg-[#F2F2F2] px-4 py-6">
            <ProgressBar
              values={[
                {
                  value: preferredVendorsPercentage,
                  color: '#518EF7',
                  label: 'Preferred Vendors',
                },
                {
                  value: nonPreferredVendorsPercentage,
                  color: '#A9D998',
                  label: 'Non - Preferred Vendors',
                },
              ]}
            />
          </div>
        </div>
      </>
    </ReportPanel>
  );
};

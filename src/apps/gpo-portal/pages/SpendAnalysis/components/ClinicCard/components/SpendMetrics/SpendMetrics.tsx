import React from 'react';
import { MetricItem } from './components/MetricItem/MetricItem';
import { getPriceString } from '@/utils';

interface SpendMetricsProps {
  totalSpend: string;
  rebateEarned: string;
  preferredShare: string;
  totalSpendYoy: number;
  rebateEarnedYoy: number;
  preferredShareYoy: number;
}

export const SpendMetrics = ({
  totalSpend,
  rebateEarned,
  preferredShare,
  totalSpendYoy,
  rebateEarnedYoy,
  preferredShareYoy,
}: SpendMetricsProps) => {
  return (
    <div className="mr-12 flex min-w-[600px] items-center rounded-[4px] border border-[rgba(56,139,255,0.15)] bg-[#388bff0d] px-6 py-4">
      <MetricItem
        label="Total Spend"
        value={getPriceString(totalSpend)}
        yoyPercentage={totalSpendYoy}
      />

      <div className="mx-4 h-8 w-[1px] bg-gray-300"></div>

      <MetricItem
        label="Rebate Earned"
        value={getPriceString(rebateEarned)}
        yoyPercentage={rebateEarnedYoy}
      />

      <div className="mx-4 h-8 w-[1px] bg-gray-300"></div>

      <MetricItem
        label="Preferred Share"
        value={preferredShare}
        yoyPercentage={preferredShareYoy}
      />
    </div>
  );
};

import { Checkbox } from '@/libs/form/Checkbox';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { SpendMetrics } from './components/SpendMetrics/SpendMetrics';
import { SpendPanel } from './components/SpendPanel/SpendPanel';
import { MarketSharePanel } from './components/MarketSharePanel/MarketSharePanel';
import { AddressType } from '@/types/common';

export interface ClinicData {
  id: string;
  name: string;
  ein: string;
  email: string;
  phoneNumber?: string;

  isActive: boolean;
  memberSince: string;

  shippingAddress: AddressType;
  billingAddress?: AddressType;
  sameAddress?: boolean;

  practiceType?: string;
  fulltimeDvmCount?: number;
  examRoomsCount?: number;
}

export interface SpendAnalysisData {
  totalSpend: string;
  rebateEarned: string;
  preferredShare: string;
  totalSpendYoy: number;
  rebateEarnedYoy: number;
  preferredShareYoy: number;

  spendPercentage: string;
  spendAmount: string;
  spendYoyPercentage: number;

  marketSharePercentage: string;
  vendorCount: string;
  marketShareYoyPercentage: number;

  annualBudget: string;
  prevYearSpend: string;
  preferredVendorsPercentage: number;
  nonPreferredVendorsPercentage: number;

  distributorData: Array<{
    name: string;
    percentage: number;
    color: string;
  }>;
  vendorData: Array<{
    name: string;
    percentage: number;
    color: string;
  }>;
  productCategoriesData: {
    parasiticides: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
    vaccines: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
    diets: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
  };
}

export interface ClinicCardProps {
  clinic: ClinicData;
  spendAnalysis: SpendAnalysisData;
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
}

export const ClinicCard = ({
  clinic,
  spendAnalysis,
  isSelected,
  onSelect,
}: ClinicCardProps) => {
  return (
    <CollapsiblePanel
      header={
        <div className="flex w-full justify-between bg-white px-4 py-3">
          <div className="flex gap-4">
            <Checkbox
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
            />
            <div>
              <div className="text-sxs mb-2 flex gap-[6px] tracking-[0.2px] text-[#98A2B3]">
                {clinic.isActive ? (
                  <p className="font-bold text-[#6AA555]">ACTIVE</p>
                ) : (
                  <p className="font-bold text-[#C74859]">INACTIVE</p>
                )}
                {clinic.ein ? (
                  <>
                    <span>-</span>
                    <p className="font-medium">#{clinic.ein}</p>
                    <span>-</span>
                  </>
                ) : null}
                <p className="italic">Member Since: {clinic.memberSince}</p>
              </div>
              <h4>{clinic.name}</h4>
              <div className="mt-3 flex items-center text-xs font-medium text-[#98A2B3]">
                {/* {clinic.practiceType && (
                  <>
                    <p>
                      Practice Type:{' '}
                      <span className="text-[#344054]">
                        {clinic.practiceType}
                      </span>
                    </p>
                    <hr className="mx-2 block h-2 w-[1px] border-0 bg-gray-300" />
                  </>
                )} */}
                {clinic.fulltimeDvmCount !== undefined && (
                  <>
                    <p>
                      # FT DVM:{' '}
                      <span className="text-[#344054]">
                        {clinic.fulltimeDvmCount}
                      </span>
                    </p>
                    <hr className="mx-2 block h-2 w-[1px] border-0 bg-gray-300" />
                  </>
                )}
                {clinic.examRoomsCount !== undefined && (
                  <p>
                    # Exam Rooms:{' '}
                    <span className="text-[#344054]">
                      {clinic.examRoomsCount}
                    </span>
                  </p>
                )}
              </div>
            </div>
          </div>
          <SpendMetrics
            totalSpend={spendAnalysis.totalSpend}
            rebateEarned={spendAnalysis.rebateEarned}
            preferredShare={spendAnalysis.preferredShare}
            totalSpendYoy={spendAnalysis.totalSpendYoy}
            rebateEarnedYoy={spendAnalysis.rebateEarnedYoy}
            preferredShareYoy={spendAnalysis.preferredShareYoy}
          />
        </div>
      }
      content={
        <div className="flex w-full flex-col gap-3 bg-white px-4 pt-6 pb-4">
          <SpendPanel
            spendPercentage={spendAnalysis.spendPercentage}
            spendAmount={spendAnalysis.spendAmount}
            yoyPercentage={spendAnalysis.spendYoyPercentage}
            totalSpend={spendAnalysis.totalSpend}
            annualBudget={spendAnalysis.annualBudget}
            prevYearSpend={spendAnalysis.prevYearSpend}
            preferredVendorsPercentage={
              spendAnalysis.preferredVendorsPercentage
            }
            nonPreferredVendorsPercentage={
              spendAnalysis.nonPreferredVendorsPercentage
            }
          />
          <MarketSharePanel
            marketSharePercentage={spendAnalysis.marketSharePercentage}
            vendorCount={spendAnalysis.vendorCount}
            yoyPercentage={spendAnalysis.marketShareYoyPercentage}
            distributorData={spendAnalysis.distributorData}
            vendorData={spendAnalysis.vendorData}
            productCategoriesData={spendAnalysis.productCategoriesData}
          />
        </div>
      }
    />
  );
};

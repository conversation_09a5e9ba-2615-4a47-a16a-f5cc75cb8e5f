import React from 'react';
import { DashboardCardWrapper } from '@/libs/dashboard/DashboardCardWrapper/DashboardCardWrapper';
import { DashboardCardLoader } from '@/libs/dashboard/DashboardCardLoader/DashboardCardLoader';
import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { useNavigate } from 'react-router-dom';
import { useSpendAnalysisSummary } from './hooks/useSpendAnalysisSummary';
import { DashboardCardError } from '@/libs/dashboard/DashboardCardError/DashboardCardError';
import { exportSheet } from '@/libs/dashboard/utils/exportSheet';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { Select } from '@/libs/form/Select';
import { useTimePeriod, PeriodOption } from '@/libs/utils/hooks/useTimePeriod';
import { ProgressBar } from '@/libs/ui/ProgressBar/ProgressBar';
import { DEFAULT_SERVER_DATE_FORMAT } from '@/constants';

interface SpendAnalysisCardProps {
  className?: string;
}

export const SpendAnalysisCard: React.FC<SpendAnalysisCardProps> = ({
  className,
}) => {
  const { data, isLoading, error, updateFilters } = useSpendAnalysisSummary();
  const navigate = useNavigate();

  const { period, setPeriod, options, startDate, endDate } = useTimePeriod({
    defaultPeriod: 'entire year',
    availableOptions: ['entire year', 'Q1', 'Q2', 'Q3', 'Q4'],
  });

  const timePeriods = options.map((option) => ({
    value: option,
    label:
      option.charAt(0).toUpperCase() +
      option.slice(1).replace(/([A-Z])/g, ' $1'),
  }));

  React.useEffect(() => {
    if (startDate && endDate) {
      updateFilters({
        date_from: startDate.format('YYYY-MM-DD'),
        date_to: endDate.format('YYYY-MM-DD'),
      });
    }
  }, [startDate, endDate, updateFilters]);

  const handleCtaClick = () => {
    navigate(GPO_ROUTES_PATH.spendAnalysis);
  };

  if (isLoading) {
    return <DashboardCardLoader title="Spend Analysis" className={className} />;
  }

  if (error || !data) {
    return <DashboardCardError className={className} title="Spend Analysis" />;
  }

  return (
    <DashboardCardWrapper
      className={className}
      title="Spend Analysis"
      headerActions={
        <>
          <div className="w-[140px]">
            <Select
              value={period}
              onChange={(e) => setPeriod(e.target.value as PeriodOption)}
              options={timePeriods}
              showEmptyOption={false}
            />
          </div>
          <Button
            variant="white"
            className="max-w-[60px]"
            aria-label="Download"
            onClick={() => {
              exportSheet({
                path: '/gpo/spend-analysis/export',
                filename: 'spend-analysis',
                format: 'xlsx',
                filters: {
                  date_from: startDate
                    ? startDate.format(DEFAULT_SERVER_DATE_FORMAT)
                    : '',
                  date_to: endDate
                    ? endDate.format(DEFAULT_SERVER_DATE_FORMAT)
                    : '',
                },
              });
            }}
          >
            <Icon name="download" aria-hidden={true} />
          </Button>
        </>
      }
    >
      <div className="mb-6 flex flex-1 items-start justify-between">
        <div>
          <p className="text-[2rem] leading-none font-bold text-[#344054]">
            {data.preferred_percent}%
          </p>
          <div className="mt-1 text-sm tracking-wide text-gray-500 uppercase">
            SPEND ON PREFERRED VENDORS
          </div>
        </div>
        <div className="mx-4 h-16 w-px bg-gray-200"></div>
        <div className="flex flex-col gap-2 text-right">
          <div className="text-sm text-[#666]">
            Total:{' '}
            <span className="font-bold text-[#333]">
              ${data.total_spend.toLocaleString()}
            </span>
          </div>
          <div className="text-sm text-[#666]">
            Preferred Vendors:{' '}
            <span className="font-bold text-[#333]">
              ${data.preferred_spend.toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      <div className="mb-6 rounded-[.5rem] bg-[#F2F8FC] p-4">
        <ProgressBar
          showLegend={false}
          values={[
            {
              value: data.preferred_percent,
              color: 'bg-blue-600',
              label: 'Preferred',
            },
            {
              value: data.non_preffered_percent,
              color: 'bg-yellow-400',
              label: 'Non-Preferred',
            },
          ]}
        />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-blue-600"></div>
            <span className="text-sm">
              Preferred (${data.preferred_spend.toLocaleString()}){' '}
              {data.preferred_percent}%
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
            <span className="text-sm">
              Non-Preferred (${data.non_preffered_spend.toLocaleString()}){' '}
              {data.non_preffered_percent}%
            </span>
          </div>
        </div>
      </div>

      <Button
        variant="white"
        size="md"
        className="min-h-[3rem]"
        onClick={handleCtaClick}
      >
        Detailed Spend Report
      </Button>
    </DashboardCardWrapper>
  );
};

import { useState, useEffect, useCallback } from 'react';
import { PlatformUsageSummaryResponse, PlatformUsageFilters } from '../types';
import { fetchApi } from '@/libs/utils/api';

export const usePlatformUsageSummary = (
  initialFilters: PlatformUsageFilters = {},
) => {
  const [data, setData] = useState<PlatformUsageSummaryResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PlatformUsageFilters>({
    date_from: undefined,
    date_to: undefined,
    ...initialFilters,
  });

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const url = `/gpo/platform-usage/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetchApi<PlatformUsageSummaryResponse>(url, {
        method: 'GET',
        authStrategy: 'token',
        redirectPath: '/gpo/login',
      });

      setData(response);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to fetch platform usage summary data';
      setError(errorMessage);
      console.error('Error fetching platform usage summary:', err);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  const updateFilters = useCallback(
    (newFilters: Partial<PlatformUsageFilters>) => {
      setFilters((prev) => ({
        ...prev,
        ...newFilters,
      }));
    },
    [],
  );

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    filters,
    updateFilters,
    refresh,
  };
};

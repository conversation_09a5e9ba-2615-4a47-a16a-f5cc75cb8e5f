import { UserDisplay } from '@/libs/auth/components/UserDisplay/UserDisplay';
import { GpoUserSectionMenu } from './components/GpoUserSectionMenu/GpoUserSectionMenu';
import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';

export const GpoUserSection = () => {
  const { user } = useAuthStore();

  return user ? (
    <UserDisplay user={user} UserSectionMenu={GpoUserSectionMenu} />
  ) : null;
};

import { Menu } from '@mantine/core';
import { Icon } from '@/libs/icons/Icon';
import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { Button } from '@/libs/ui/Button/Button';

export const GpoUserSectionMenu = () => {
  const { logout } = useAuthStore();

  return (
    // TODO: Add change password modal
    <Menu width={200} shadow="md" position="right-end">
      <Menu.Target>
        <Button variant="unstyled" aria-label="User menu options">
          <Icon name="moreOptions" color="#333" aria-hidden={true} />
        </Button>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Item onClick={logout} className="text-red-600">
          <div className="flex items-center">
            <Icon name="moreOptions" size={16} className="mr-2" />
            Logout
          </div>
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

import { Flex, Text } from '@mantine/core';
import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { TopNavbar } from '@/libs/ui/TopNavbar/TopNavbar';

export const GpoTopNavbar = () => {
  const { user } = useAuthStore();

  if (!user) {
    return null;
  }

  return (
    <TopNavbar>
      <Flex gap="1rem" align="center" justify="space-between" w="100%">
        <Text fw="600" size="lg">
          GPO Portal
        </Text>
      </Flex>
    </TopNavbar>
  );
};

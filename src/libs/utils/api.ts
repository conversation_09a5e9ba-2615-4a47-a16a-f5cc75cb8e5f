import { getActiveClinic } from '@/libs/clinics/utils/activeClinic';
import { tokenManager } from '@/libs/auth/utils/tokenManager';

const BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

type AuthStrategy = 'cookie' | 'token';
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

type RequestOptions = {
  headers?: Record<string, string>;
  suppressAuthRedirect?: boolean;
  authStrategy?: AuthStrategy;
  redirectPath?: string;
};

interface FetchApiConfig {
  method?: HttpMethod;
  body?: unknown;
  withApi?: boolean;
  authStrategy?: AuthStrategy;
  redirectPath?: string;
  options?: RequestOptions;
}

const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

const refreshXsrfToken = async (): Promise<void> => {
  await fetch(`${BASE_URL}/sanctum/csrf-cookie`, {
    credentials: 'include',
  });
};

const getXsrfTokenFromCookie = (): string | null => {
  const token = getCookie('XSRF-TOKEN');
  return token ? decodeURIComponent(token) : null;
};

export const fetchApi = async <T>(
  url: string,
  config: FetchApiConfig = {},
): Promise<T> => {
  const {
    method = 'GET',
    body,
    withApi = true,
    authStrategy = 'cookie',
    redirectPath,
    options = {},
  } = config;
  const finalRedirectPath = redirectPath || '/login';

  if (authStrategy === 'cookie') {
    await refreshXsrfToken();
  }

  const xsrfToken = authStrategy === 'cookie' ? getXsrfTokenFromCookie() : null;
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    ...options.headers,
  };

  if (authStrategy === 'cookie') {
    headers['X-XSRF-TOKEN'] = xsrfToken || '';
    headers['Highfive-Clinic'] = getActiveClinic()?.id || '';
  } else if (authStrategy === 'token') {
    const authHeader = tokenManager.getAuthHeader();
    if (authHeader) {
      headers.Authorization = authHeader;
    }
  }

  const response = await fetch(`${BASE_URL}${withApi ? '/api' : ''}${url}`, {
    method,
    headers,
    body: method !== 'GET' && body ? JSON.stringify(body) : undefined,
    credentials: authStrategy === 'cookie' ? 'include' : 'omit',
  });

  if (response.status === 401 && !options.suppressAuthRedirect) {
    if (authStrategy === 'token') {
      tokenManager.removeToken();
    }
    window.location.replace(finalRedirectPath);
    throw new Error('Unauthorized');
  }

  if (response.status === 204) {
    return response as T;
  }

  if (response.ok) {
    try {
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        return (await response.json()) as T;
      }

      return response as T;
    } catch {
      return response as T;
    }
  } else {
    throw { apiResponse: response, data: await response.json() };
  }
};

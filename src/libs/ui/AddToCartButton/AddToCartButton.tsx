import { Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/libs/icons/Icon';
import { cva, type VariantProps } from 'class-variance-authority';

const addToCartButtonVariants = cva('@container', {
  variants: {
    height: {
      sm: 'sm',
      normal: 'md',
    },
  },
  defaultVariants: {
    height: 'normal',
  },
});

type AddToCartButtonProps = {
  disabled?: boolean;
  isLoading?: boolean;
  onClick?: () => void;
  height?: VariantProps<typeof addToCartButtonVariants>['height'];
};

export const AddToCartButton = ({
  disabled,
  isLoading = false,
  height = 'normal',
  onClick,
}: AddToCartButtonProps) => {
  const { t } = useTranslation();

  return (
    <Button
      disabled={disabled}
      loading={isLoading}
      size={height === 'sm' ? 'sm' : 'md'}
      onClick={onClick}
      className={addToCartButtonVariants({ height })}
    >
      <div className="flex items-center justify-center @[120px]:hidden">
        <Icon name="cartSummary" aria-hidden="true" />
      </div>
      <Text className="hidden text-center @[120px]:block">
        {t('client.search.addToCart')}
      </Text>
    </Button>
  );
};

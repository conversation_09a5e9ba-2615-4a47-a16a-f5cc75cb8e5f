.container {
  border-radius: 0.25rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.header {
  background-color: rgba(0, 0, 0, 0.03);
  width: 100%;
  position: relative;
  min-height: 2.6rem;
  display: flex;
  align-items: center;

  &.cleanHeader {
    background-color: transparent;
  }
}

.collapseButton {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #fff;
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  transition: 0.3s;

  &[data-open='true'] {
    transform: translateY(-50%) rotate(-180deg);
  }
}

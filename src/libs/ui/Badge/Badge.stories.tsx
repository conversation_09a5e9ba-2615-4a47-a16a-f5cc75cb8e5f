import type { Meta, StoryObj } from '@storybook/react-vite';
import { Badge, type BadgeProps } from './Badge';
import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

type Story = StoryObj<typeof Badge>;

const props: Omit<BadgeProps, 'children'>[] = [
  { className: 'bg-[#89BF77] text-white' },
  { className: 'bg-[#B6F5F9] text-[#344054]' },
  { className: 'text-[#344054]' },
];

const Wrap = () => {
  return (
    <Flex gap="md" p="lg">
      {props.map((prop, index) => (
        <Badge key={+index} {...prop}>
          Text
        </Badge>
      ))}
      <Badge {...props[2]}>
        <Text lh="2rem">Custom text</Text>
      </Badge>
    </Flex>
  );
};

const meta: Meta<typeof Badge> = {
  title: 'UI/Badge',
  component: Wrap,
};
export default meta;

export const Default: Story = {
  args: {},
};

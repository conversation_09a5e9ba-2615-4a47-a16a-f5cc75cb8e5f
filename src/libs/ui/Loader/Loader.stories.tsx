import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { Loader } from './Loader';

type Story = StoryObj<typeof Loader>;

const meta: Meta<typeof Loader> = {
  title: 'UI/Loader',
  component: Loader,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    color: {
      control: 'color',
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl', 30, '2rem'],
    },
    type: {
      control: 'select',
      options: ['oval', 'bars', 'dots'],
    },
  },
};

export default meta;

export const Default: Story = {
  args: {
    color: '#228be6',
    size: 'md',
    type: 'oval',
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="text-center">
        <Loader size="xs" />
        <p className="mt-2 text-sm">xs</p>
      </div>
      <div className="text-center">
        <Loader size="sm" />
        <p className="mt-2 text-sm">sm</p>
      </div>
      <div className="text-center">
        <Loader size="md" />
        <p className="mt-2 text-sm">md</p>
      </div>
      <div className="text-center">
        <Loader size="lg" />
        <p className="mt-2 text-sm">lg</p>
      </div>
      <div className="text-center">
        <Loader size="xl" />
        <p className="mt-2 text-sm">xl</p>
      </div>
    </div>
  ),
};

export const Types: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <Loader type="oval" />
        <p className="mt-2 text-sm">Oval</p>
      </div>
      <div className="text-center">
        <Loader type="bars" />
        <p className="mt-2 text-sm">Bars</p>
      </div>
      <div className="text-center">
        <Loader type="dots" />
        <p className="mt-2 text-sm">Dots</p>
      </div>
    </div>
  ),
};

export const Colors: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Loader color="#228be6" />
      <Loader color="#fa5252" />
      <Loader color="#40c057" />
      <Loader color="#fd7e14" />
      <Loader color="#7c3aed" />
    </div>
  ),
};

export const CustomSize: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="text-center">
        <Loader size={20} />
        <p className="mt-2 text-sm">20px</p>
      </div>
      <div className="text-center">
        <Loader size="3rem" />
        <p className="mt-2 text-sm">3rem</p>
      </div>
    </div>
  ),
};

export const WithChildren: Story = {
  render: () => (
    <Loader>
      <div className="font-medium text-blue-600">Loading...</div>
    </Loader>
  ),
};

import { forwardRef, type ReactNode } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils/tailwind';

// Loader size variants matching Mantine's size system
const loaderVariants = cva('inline-block', {
  variants: {
    size: {
      xs: 'w-4 h-4',
      sm: 'w-5 h-5',
      md: 'w-6 h-6',
      lg: 'w-8 h-8',
      xl: 'w-10 h-10',
    },
    type: {
      oval: '',
      bars: '',
      dots: '',
    },
  },
  defaultVariants: {
    size: 'md',
    type: 'oval',
  },
});

// Oval loader animation
const ovalVariants = cva('border-2 border-solid rounded-full animate-spin', {
  variants: {
    size: {
      xs: 'border-[1.5px]',
      sm: 'border-[1.5px]',
      md: 'border-2',
      lg: 'border-2',
      xl: 'border-[3px]',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// Bars loader container
const barsContainerVariants = cva('flex items-end justify-center gap-0.5', {
  variants: {
    size: {
      xs: 'gap-[1px]',
      sm: 'gap-[1px]',
      md: 'gap-0.5',
      lg: 'gap-0.5',
      xl: 'gap-1',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// Individual bar in bars loader
const barVariants = cva('loader-bars', {
  variants: {
    size: {
      xs: 'w-0.5',
      sm: 'w-0.5',
      md: 'w-1',
      lg: 'w-1',
      xl: 'w-1.5',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// Dots loader container
const dotsContainerVariants = cva('flex items-center justify-center gap-1', {
  variants: {
    size: {
      xs: 'gap-0.5',
      sm: 'gap-0.5',
      md: 'gap-1',
      lg: 'gap-1',
      xl: 'gap-1.5',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// Individual dot in dots loader
const dotVariants = cva('rounded-full loader-dots', {
  variants: {
    size: {
      xs: 'w-1 h-1',
      sm: 'w-1 h-1',
      md: 'w-1.5 h-1.5',
      lg: 'w-2 h-2',
      xl: 'w-2.5 h-2.5',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export type LoaderProps = {
  /** Loader color, any valid CSS color value */
  color?: string;
  /** Loader size */
  size?: VariantProps<typeof loaderVariants>['size'] | number | string;
  /** Loader type */
  type?: VariantProps<typeof loaderVariants>['type'];
  /** Custom content to render instead of loader */
  children?: ReactNode;
  /** Additional CSS classes */
  className?: string;
} & Omit<React.HTMLAttributes<HTMLDivElement>, 'color'>;

export const Loader = forwardRef<HTMLDivElement, LoaderProps>(
  (
    {
      color = '#228be6',
      size = 'md',
      type = 'oval',
      children,
      className,
      ...props
    },
    ref,
  ) => {
    // If children are provided, render them instead of the loader
    if (children) {
      return (
        <div ref={ref} className={className} {...props}>
          {children}
        </div>
      );
    }

    // Handle custom size (number or string)
    const sizeVariant =
      typeof size === 'string' && ['xs', 'sm', 'md', 'lg', 'xl'].includes(size)
        ? (size as VariantProps<typeof loaderVariants>['size'])
        : 'md';

    const customSize =
      typeof size === 'number'
        ? `${size}px`
        : typeof size === 'string' &&
            !['xs', 'sm', 'md', 'lg', 'xl'].includes(size)
          ? size
          : undefined;

    const baseClasses = loaderVariants({ size: sizeVariant, type });
    const containerClasses = mergeClasses(baseClasses, className);

    const style = customSize ? { width: customSize, height: customSize } : {};

    // Render oval loader
    if (type === 'oval') {
      const ovalClasses = ovalVariants({ size: sizeVariant });
      // Convert color to rgba with 20% opacity for border
      const borderColor = color.startsWith('#')
        ? `${color}33`
        : `rgba(34, 139, 230, 0.2)`; // fallback for non-hex colors
      const borderTopColor = color;

      return (
        <div
          ref={ref}
          className={mergeClasses(containerClasses, ovalClasses)}
          style={{
            ...style,
            borderColor,
            borderTopColor,
          }}
          {...props}
        />
      );
    }

    // Render bars loader
    if (type === 'bars') {
      const containerClass = barsContainerVariants({ size: sizeVariant });
      const barClass = barVariants({ size: sizeVariant });

      return (
        <div
          ref={ref}
          className={mergeClasses(containerClasses, containerClass)}
          style={style}
          {...props}
        >
          {[0, 1, 2, 3, 4].map((index) => (
            <div
              key={index}
              className={barClass}
              style={{
                height: '100%',
                backgroundColor: color,
                animationDelay: `${index * 0.1}s`,
              }}
            />
          ))}
        </div>
      );
    }

    // Render dots loader
    if (type === 'dots') {
      const containerClass = dotsContainerVariants({ size: sizeVariant });
      const dotClass = dotVariants({ size: sizeVariant });

      return (
        <div
          ref={ref}
          className={mergeClasses(containerClasses, containerClass)}
          style={style}
          {...props}
        >
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className={dotClass}
              style={{
                backgroundColor: color,
                animationDelay: `${index * 0.2}s`,
              }}
            />
          ))}
        </div>
      );
    }

    return null;
  },
);

Loader.displayName = 'Loader';

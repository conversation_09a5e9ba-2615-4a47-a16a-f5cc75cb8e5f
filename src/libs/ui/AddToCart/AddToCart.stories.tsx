import type { Meta, StoryObj } from '@storybook/react-vite';
import { AddToCart } from './AddToCart';
import { Flex } from '@/libs/ui/Flex/Flex';

const meta: Meta<typeof AddToCart> = {
  title: 'Product/AddToCart',
  component: AddToCart,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    productOfferId: {
      control: 'text',
      description: 'Unique identifier for the product offer',
    },
    minIncrement: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Minimum increment for quantity changes',
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
      description: 'Size of the component',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    productOfferId: 'test-product-1',
    minIncrement: 1,
    size: 'md',
  },
};

export const WithMinIncrement: Story = {
  args: {
    productOfferId: 'test-product-2',
    minIncrement: 5,
    size: 'md',
  },
};

export const SmallSize: Story = {
  args: {
    productOfferId: 'test-product-3',
    minIncrement: 1,
    size: 'sm',
  },
};

export const LargeSize: Story = {
  args: {
    productOfferId: 'test-product-4',
    minIncrement: 1,
    size: 'lg',
  },
};

export const SizeComparison: Story = {
  render: () => (
    <Flex direction="column" gap="lg" align="center">
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>Small</h4>
        <AddToCart
          productOfferId="size-comparison-sm"
          minIncrement={1}
          size="sm"
        />
      </div>
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>Medium</h4>
        <AddToCart
          productOfferId="size-comparison-md"
          minIncrement={1}
          size="md"
        />
      </div>
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>Large</h4>
        <AddToCart
          productOfferId="size-comparison-lg"
          minIncrement={1}
          size="lg"
        />
      </div>
    </Flex>
  ),
};

export const DifferentIncrements: Story = {
  render: () => (
    <Flex direction="column" gap="lg" align="center">
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>
          Increment: 1
        </h4>
        <AddToCart productOfferId="increment-1" minIncrement={1} size="md" />
      </div>
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>
          Increment: 5
        </h4>
        <AddToCart productOfferId="increment-5" minIncrement={5} size="md" />
      </div>
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>
          Increment: 10
        </h4>
        <AddToCart productOfferId="increment-10" minIncrement={10} size="md" />
      </div>
    </Flex>
  ),
};

export const ButtonBehaviorDemo: Story = {
  render: () => (
    <Flex direction="column" gap="lg" align="center">
      <div style={{ textAlign: 'center' }}>
        <h3 style={{ marginBottom: '16px' }}>
          Button Behavior Based on Quantity
        </h3>
        <p style={{ marginBottom: '16px', color: '#666' }}>
          The left button changes from minus to trash when quantity reaches
          minIncrement
        </p>
      </div>
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>
          Quantity {'>'} minIncrement (shows minus button)
        </h4>
        <p
          style={{
            marginBottom: '8px',
            textAlign: 'center',
            fontSize: '14px',
            color: '#666',
          }}
        >
          minIncrement: 3, simulated quantity: 9
        </p>
        <AddToCart productOfferId="demo-minus" minIncrement={3} size="md" />
      </div>
      <div>
        <h4 style={{ marginBottom: '8px', textAlign: 'center' }}>
          Quantity = minIncrement (shows trash button)
        </h4>
        <p
          style={{
            marginBottom: '8px',
            textAlign: 'center',
            fontSize: '14px',
            color: '#666',
          }}
        >
          minIncrement: 5, simulated quantity: 5
        </p>
        <AddToCart productOfferId="demo-trash" minIncrement={5} size="md" />
      </div>
    </Flex>
  ),
};

export interface MoniteTokenResponse {
  accessToken: string;
  entityId: string;
  entityUserId: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface MoniteConfig {
  entityId: string;
  api_url: string;
  fetchToken: (clinicId: string) => Promise<MoniteTokenResponse>;
  locale?: {
    code: string;
    messages?: Record<string, string>;
  };
}

export interface PayablesTableProps {
  pageSize?: number;
  pageSizeOptions?: number[];
  enableSorting?: boolean;
  enableFiltering?: boolean;
}

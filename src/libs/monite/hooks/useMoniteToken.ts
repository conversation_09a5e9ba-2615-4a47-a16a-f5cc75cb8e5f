import { useCallback, useState, useEffect } from 'react';
import type { MoniteConfig, MoniteTokenResponse } from '@/libs/monite/types';
import { fetchToken } from '../services/fetchMoniteToken';

export interface UseMoniteTokenProps {
  clinicId: string;
}

export interface UseMoniteTokenReturn {
  token: MoniteTokenResponse | undefined;
  isLoading: boolean;
  hasError: boolean;
  fetchToken: MoniteConfig['fetchToken'];
  handleFetchToken: () => Promise<void>;
  error: string | null;
}

export const useMoniteToken = ({
  clinicId,
}: UseMoniteTokenProps): UseMoniteTokenReturn => {
  const [token, setToken] = useState<MoniteTokenResponse | undefined>(
    undefined,
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleFetchToken = useCallback(async () => {
    if (!clinicId) return;

    setIsLoading(true);
    setHasError(false);
    setError(null);

    try {
      const result = await fetchToken(clinicId);
      setToken(result);
    } catch (err: unknown) {
      setHasError(true);
      if (err && typeof err === 'object' && 'data' in err) {
        const apiError = err as { data?: { message?: string } };
        setError(apiError.data?.message || 'Failed to fetch Monite token');
      } else {
        setError('Failed to fetch Monite token');
      }
    } finally {
      setIsLoading(false);
    }
  }, [clinicId]);

  // Auto-fetch token when component mounts
  useEffect(() => {
    handleFetchToken();
  }, [handleFetchToken]);

  return {
    token,
    isLoading,
    hasError,
    handleFetchToken,
    fetchToken,
    error,
  };
};

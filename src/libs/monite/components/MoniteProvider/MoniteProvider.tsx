import { ReactNode } from 'react';
import { MoniteProvider as MoniteSDKProvider } from '@monite/sdk-react';
import { MONITE_LOCALE, MONITE_THEME } from '@/libs/monite/constants';
import { MoniteTokenResponse, MoniteConfig } from '@/libs/monite/types';

interface MoniteProviderProps {
  clinicId: string;
  children: ReactNode;
  token: MoniteTokenResponse;
  fetchToken: MoniteConfig['fetchToken'];
}

export const MoniteProvider = ({
  clinicId,
  children,
  token,
  fetchToken,
}: MoniteProviderProps) => {
  const fetchTokenMoniteFormat = async () => {
    const { accessToken, expiresIn, tokenType } = await fetchToken(clinicId);
    return {
      access_token: accessToken,
      expires_in: expiresIn,
      token_type: tokenType,
    };
  };

  const moniteConfig = {
    entityId: token.entityId,
    fetchToken: fetchTokenMoniteFormat,
    token: token.accessToken,
  };

  return (
    <MoniteSDKProvider
      monite={moniteConfig}
      locale={MONITE_LOCALE}
      theme={MONITE_THEME}
    >
      {children}
    </MoniteSDKProvider>
  );
};

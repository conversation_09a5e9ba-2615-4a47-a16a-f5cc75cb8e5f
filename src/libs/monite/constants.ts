export const MONITE_API_URL =
  import.meta.env.VITE_MONITE_API_URL || 'https://api.sandbox.monite.com/v1';

export const MONITE_LOCALE = {
  code: 'en-US',
  messages: {
    // Main navigation and sections
    Payables: 'Invoices',
    Counterparts: 'Vendors',
    Sales: 'Sales',
    Invoices: 'Invoices',

    // Form labels and actions
    'Counterpart Name': 'Vendor Name',
    'Create New': 'Create New',
    'Add New': 'Add New',
    Edit: 'Edit',
    Delete: 'Delete',
    Save: 'Save',
    Cancel: 'Cancel',

    // Status and states
    Draft: 'Draft',
    Sent: 'Sent',
    Paid: 'Paid',
    Overdue: 'Overdue',
    Cancelled: 'Cancelled',

    // Common terms
    Amount: 'Amount',
    Date: 'Date',
    'Due Date': 'Due Date',
    Status: 'Status',
    Description: 'Description',
    Notes: 'Notes',
  },
};

export const MONITE_COMPONENT_SETTINGS = {
  payables: {
    pageSizeOptions: [15, 50, 100],
  },
} as const;

export const MONITE_THEME = {
  colors: {
    primary: '#fadf82',
  },
  typography: {
    fontFamily: 'var(--font-sans)',
    fontSizes: {
      xxl: 'calc(2.5rem * var(--mantine-scale))',
      xl: 'calc(2rem * var(--mantine-scale))',
      xlLg: 'calc(1.5rem * var(--mantine-scale))',
      lg: 'calc(1.25rem * var(--mantine-scale))',
    },
  },
};

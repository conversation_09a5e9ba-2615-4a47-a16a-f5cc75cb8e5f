import { DashboardCardWrapper } from '../DashboardCardWrapper/DashboardCardWrapper';

interface DashboardCardErrorProps {
  title: string;
  className?: string;
}

export const DashboardCardError = ({
  title,
  className,
}: DashboardCardErrorProps) => {
  return (
    <DashboardCardWrapper
      title={title}
      headerActions={null}
      className={className}
    >
      <div className="flex h-full items-center justify-center bg-[#f2f2f2]">
        <p className="text-[#333]">Error on load</p>
      </div>
    </DashboardCardWrapper>
  );
};

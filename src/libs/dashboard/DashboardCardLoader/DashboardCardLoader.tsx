import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { DashboardCardWrapper } from '../DashboardCardWrapper/DashboardCardWrapper';

interface DashboardCardLoaderProps {
  title: string;
  className?: string;
}

export const DashboardCardLoader = ({
  title,
  className,
}: DashboardCardLoaderProps) => {
  return (
    <DashboardCardWrapper
      className={className}
      title={title}
      headerActions={
        <div className="flex gap-3">
          <Skeleton width={140} height={40} />
          <Skeleton width={60} height={40} />
        </div>
      }
    >
      <div className="mb-6 flex items-center justify-between gap-4">
        <div className="flex flex-1 flex-col gap-1">
          <Skeleton width={140} height={32} />
          <Skeleton width={180} height={20} />
        </div>
        <div className="flex flex-1 flex-col items-end gap-2">
          <Skeleton width={140} height={20} />
          <Skeleton width={180} height={20} />
        </div>
      </div>
      <div className="flex flex-col gap-5">
        <Skeleton width="100%" height={165} borderRadius={6} />
        <Skeleton width="100%" height={46} borderRadius={6} />
      </div>
    </DashboardCardWrapper>
  );
};

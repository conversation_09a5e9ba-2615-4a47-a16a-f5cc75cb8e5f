import React, { ReactNode } from 'react';

interface DashboardCardWrapperProps {
  className?: string;
  title: string;
  headerActions: ReactNode;
  children: ReactNode;
}

export const DashboardCardWrapper: React.FC<DashboardCardWrapperProps> = ({
  className,
  title,
  children,
  headerActions,
}) => {
  return (
    <div
      className={`flex flex-col rounded-lg border border-gray-200 bg-white px-[1.5rem] py-[2rem] ${className || ''}`}
    >
      <div className="mb-8 flex items-center justify-between border-b border-gray-200 pb-8">
        <h2 className="text-base font-medium text-gray-900">{title}</h2>
        <div className="flex w-1/2 items-center justify-end gap-3">
          {headerActions}
        </div>
      </div>

      {children}
    </div>
  );
};

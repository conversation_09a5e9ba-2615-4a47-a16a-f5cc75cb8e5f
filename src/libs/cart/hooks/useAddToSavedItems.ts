import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { fetchApi } from '@/libs/utils/api';
import { SavedItemType } from '@/types';
import { ApiErrorProps } from '@/types/utility';
import { apiErrorNotification } from '@/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';

const addSavedItem = async (cartItemIds: string[]): Promise<SavedItemType> => {
  return fetchApi<SavedItemType>(`/saved-items/add`, {
    method: 'POST',
    body: {
      cartItemIds,
    },
  });
};

export const useAddToSavedItems = () => {
  const queryClient = useQueryClient();
  const { fetchCart } = useCartStore();
  return useMutation({
    mutationFn: (cartItemIds: string[]) => addSavedItem(cartItemIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.savedItems.all,
      });

      fetchCart();
    },
    onError: (error) => {
      const apiError = error as unknown as ApiErrorProps;
      const errorMessage =
        apiError.data?.message || 'Failed to add item to saved items';
      apiErrorNotification(errorMessage);
    },
  });
};

import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { fetchApi } from '@/libs/utils/api';
import { SavedItemType } from '@/types';
import { ApiErrorProps } from '@/types/utility';
import { apiErrorNotification } from '@/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';

const moveSavedItemToCart = async (
  savedItemIds: string[],
): Promise<SavedItemType> => {
  return fetchApi<SavedItemType>(`/saved-items/move-to-cart`, {
    method: 'POST',
    body: {
      savedItemIds,
    },
  });
};

export const useMoveSavedToCart = () => {
  const queryClient = useQueryClient();
  const { fetchCart } = useCartStore();
  return useMutation({
    mutationFn: (savedItemIds: string[]) => moveSavedItemToCart(savedItemIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.savedItems.all,
      });

      fetchCart();
    },
    onError: (error) => {
      const apiError = error as unknown as ApiErrorProps;
      const errorMessage =
        apiError.data?.message || 'Failed to add saved items to cart';
      apiErrorNotification(errorMessage);
    },
  });
};

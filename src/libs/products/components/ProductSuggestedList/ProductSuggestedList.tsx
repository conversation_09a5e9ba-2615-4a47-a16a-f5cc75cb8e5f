import { ProductType } from '@/types';
import { Flex } from '@/libs/ui/Flex/Flex';
import { SuggestedOfferItem } from '../SuggestedOfferList/SuggestedOfferItem';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { ErrorSection } from '@/components';

export type ProductSuggestedListProps = {
  products: ProductType[];
  isLoading?: boolean;
  isError?: boolean;
  errorMessage?: string;
};

export const ProductSuggestedList = ({
  products = [],
  isLoading = false,
  isError = false,
  errorMessage = 'Failed to load alternatives',
}: ProductSuggestedListProps) => {
  if (isLoading) {
    return <ContentLoader />;
  }

  if (isError) {
    return <ErrorSection title={errorMessage} />;
  }

  if (products.length === 0) {
    return null;
  }

  const offers = products.map((product) => ({
    ...product.offers[0],
    product,
  }));

  return (
    <Flex direction="column">
      {offers.map((offer) => (
        <SuggestedOfferItem key={offer.id} offer={offer} enableVendorSwap />
      ))}
    </Flex>
  );
};

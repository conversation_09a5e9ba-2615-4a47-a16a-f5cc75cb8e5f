// Replace your-renderer with the renderer you are using (e.g., react, vue3, etc.)
import type { Meta, StoryObj } from '@storybook/react-vite';

import {
  ProductSuggestedList,
  type ProductSuggestedListProps,
} from './ProductSuggestedList';
import { ProductType } from '@/types';

const Wrapper = (props: ProductSuggestedListProps) => (
  <div className="max-w-[600px]">
    <ProductSuggestedList {...props} />
  </div>
);

const meta: Meta<typeof ProductSuggestedList> = {
  title: 'Product/ProductSuggestedList',
  component: Wrapper,
};
export default meta;

type Story = StoryObj<typeof ProductSuggestedList>;

const productsData = [
  {
    id: '9d756f5e-6c0f-4e42-b747-866455574881',
    name: 'Adequan® Canine 5 ml Vials, 2/Pkg',
    imageUrl:
      'https://content.pattersonvet.com/items/LargeSquare/images/JPG_1096511.jpg',
    price: '0.00',
    clinicPrice: null,
    isFavorite: false,
    lastOrderedAt: null,
    lastOrderedQuantity: null,
    stockStatus: 'IN_STOCK',
    vendorSku: '07-893-4465',
    manufacturer: 'AMERICAN REGENT',
    manufacturerSku: '10797097502',
    description:
      'Recommended for intramuscular injection for the control of signs associated with non-infectious degenerative and/or traumatic arthritis of canine synovial joints Helps keep the cartilage healthy and intact so that the bone in the joint cannot touch other bones Should be used with caution in dogs with renal or hepatic impairment',
    increments: 1,
    vendor: {
      id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
      name: 'Patterson',
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
    },
    attributes: [
      {
        name: 'Volume',
        value: '5 ml',
      },
      {
        name: 'Strength',
        value: '100 mg/ml',
      },
      {
        name: 'NDC Number',
        value: '10797-0975-02',
      },
    ],
    quantityInCart: 0,
    swapSet: [],
  },
  {
    id: '9d792726-fccf-4ae2-a34e-0f8db13acaae',
    name: 'Surflo ETFE IV Catheter, Green, 18g x 2.0"',
    imageUrl:
      'https://as.mwiah.com/media/image?id=ff63223e-cd8b-e011-b5d8-001ec95a9f48',
    price: '1.50',
    clinicPrice: null,
    isFavorite: false,
    lastOrderedAt: null,
    lastOrderedQuantity: null,
    stockStatus: 'IN_STOCK',
    vendorSku: '008446',
    manufacturer: 'Terumo',
    manufacturerSku: 'SROX1851CA',
    description: 'Surflo ETFE IV Catheter, Green, 18g x 2.0"',
    increments: 1,
    vendor: {
      id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
      name: 'MWI',
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
    },
    attributes: [],
    quantityInCart: 0,
    swapSet: [
      {
        id: '9d7571b8-52f4-4fe4-8a43-e56237976282',
        vendorName: 'Midwest',
        price: '1.50',
      },
      {
        id: '9d757f69-79d3-4c37-8449-a3ad41edd22f',
        vendorName: 'Victor Medical Group',
        price: '1.64',
      },
    ],
  },
  {
    id: '9d79272d-64ee-4655-8491-3b500dd7eec3',
    name: 'Sur-Vet Surflo ETFE IV Catheter, Green, 18g x 1.25"',
    imageUrl:
      'https://as.mwiah.com/media/image?id=ff63223e-cd8b-e011-b5d8-001ec95a9f48',
    price: '1.22',
    clinicPrice: null,
    isFavorite: false,
    lastOrderedAt: null,
    lastOrderedQuantity: null,
    stockStatus: 'IN_STOCK',
    vendorSku: '012213',
    manufacturer: 'Terumo',
    manufacturerSku: 'SROX1832V',
    description: 'Sur-Vet Surflo ETFE IV Catheter, Green, 18g x 1.25"',
    increments: 1,
    vendor: {
      id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
      name: 'MWI',
      imageUrl:
        'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
    },
    attributes: [],
    quantityInCart: 0,
    swapSet: [
      {
        id: '9d756fd2-9332-41a6-a027-afb2c2827a5a',
        vendorName: 'Midwest',
        price: '1.26',
      },
      {
        id: '9d757f69-8268-46a8-8215-10e615f39a32',
        vendorName: 'Victor Medical Group',
        price: '1.33',
      },
      {
        id: '9d757377-2c55-4682-bf93-57b5a827b6b9',
        vendorName: 'Covetrus',
        price: '1.89',
      },
    ],
  },
] as unknown as ProductType[];

export const Default: Story = {
  args: {
    products: productsData,
  },
};

import { getActiveClinic } from '@/libs/clinics/utils/activeClinic';
import dayjs from 'dayjs';
import { PurchaseHistoryChartData } from './types';
import { fetchApi } from '@/libs/utils/api';

export const renderCustomBarLabel = ({
  x,
  y,
  width,
  value,
}: {
  x: number;
  y: number;
  width: number;
  value: number;
}) => {
  return (
    <text
      x={x + width / 2}
      y={y}
      fill="#666"
      textAnchor="middle"
      dy={-6}
      fontSize={12}
      fontWeight={500}
    >
      {value === 0 ? '' : value}
    </text>
  );
};

export const renderCustomAxisTick = ({
  x,
  y,
  payload,
}: {
  x: number;
  y: number;
  payload: { value: string };
}) => {
  const date = dayjs(payload.value, 'YYYY-MM');
  return (
    <text
      fill="rgba(51, 51, 51, 0.5)"
      y={y + 10}
      x={x}
      fontSize={10}
      fontWeight={500}
      textAnchor="middle"
    >
      {date.format('MMM/YY').toUpperCase()}
    </text>
  );
};

export const fetchPurchaseHistoryData = async (productId: string) => {
  const activeClinic = getActiveClinic();

  const data = await fetchApi<PurchaseHistoryChartData>(
    `/clinics/${activeClinic?.id}/product-offers/${productId}/purchase-history`,
  );

  return data;
};

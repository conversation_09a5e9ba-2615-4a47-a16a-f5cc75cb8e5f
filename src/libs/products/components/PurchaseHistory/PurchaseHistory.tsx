import { Divider, Modal, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Button } from '@/libs/ui/Button/Button';
import styles from './PurchaseHistory.module.css';
import { useDisclosure } from '@mantine/hooks';
import { PurchaseHistoryChart } from './PurchaseHistoryChart';
import { LastOrderedOn } from '@/libs/ui/LastOrderedOn/LastOrderedOn';

type PurchaseHistoryProps = {
  productId: string;
  lastOrderedQuantity: string | number | null;
  lastOrderedAt: string | null;
};

export const PurchaseHistory = ({
  productId,
  lastOrderedQuantity,
  lastOrderedAt,
}: PurchaseHistoryProps) => {
  const [isModalOpen, { open, close }] = useDisclosure(false);

  if (!lastOrderedAt || !lastOrderedQuantity) {
    return null;
  }

  return (
    <Flex align="center" justify="space-between" className={styles.container}>
      <LastOrderedOn
        lastOrderedQuantity={lastOrderedQuantity}
        lastOrderedAt={lastOrderedAt}
      />
      <Divider orientation="vertical" />
      <Button
        variant="unstyled"
        onClick={open}
        style={{ padding: '0.375rem 0' }}
      >
        <Text size="0.75rem" c="#447BFD" fw="500">
          Purchase History
        </Text>
      </Button>
      <Modal
        opened={isModalOpen}
        onClose={close}
        title="Purchase History"
        size="auto"
      >
        <div className="p-4">
          <PurchaseHistoryChart productId={productId} />
        </div>
      </Modal>
    </Flex>
  );
};

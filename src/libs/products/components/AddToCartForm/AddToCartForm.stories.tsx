import type { Meta, StoryObj } from '@storybook/react-vite';
import { AddToCartForm } from './AddToCartForm';
import { Flex } from '@/libs/ui/Flex/Flex';

type Story = StoryObj<typeof AddToCartForm>;

const Wrap = () => {
  return (
    <Flex gap="xl">
      <AddToCartForm increments={1} productOfferId="1234" />
    </Flex>
  );
};

const meta: Meta<typeof AddToCartForm> = {
  title: 'Product/AddToCartForm',
  component: Wrap,
};
export default meta;

export const Default: Story = {
  args: {},
};

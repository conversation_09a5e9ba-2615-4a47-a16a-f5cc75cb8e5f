import { ReactNode } from 'react';
import InStockIcon from './assets/in-stock.svg?react';
import DropShipIcon from './assets/drop-ship.svg?react';
import BackorderIcon from './assets/backorder.svg?react';
import SpecialOrderIcon from './assets/special-order.svg?react';
import OutOfStockIcon from './assets/out-of-stock.svg?react';
import { Tooltip } from '@/libs/ui/Tooltip';
import { Flex } from '@/libs/ui/Flex/Flex';
import { StockStatusType } from '@/libs/products/types';

interface StockStatusIconProps {
  status: StockStatusType;
}

export const stockStatusConfigs: Record<
  StockStatusType,
  {
    name: string;
    icon: ReactNode;
  }
> = {
  IN_STOCK: {
    name: 'In Stock',
    icon: <InStockIcon />,
  },
  DROP_SHIP: {
    name: 'Drop Ship',
    icon: <DropShipIcon />,
  },
  BACKORDER: {
    name: 'Backorder',
    icon: <BackorderIcon />,
  },
  OUT_OF_STOCK: {
    name: 'Out of Stock',
    icon: <OutOfStockIcon />,
  },
  SPECIAL_ORDER: {
    name: 'Special Order',
    icon: <SpecialOrderIcon />,
  },
};

export const StockStatusIcon = ({ status }: StockStatusIconProps) => {
  const { icon, name } = stockStatusConfigs[status];

  return (
    <Tooltip label={name}>
      <Flex align="center" justify="center">
        {icon}
      </Flex>
    </Tooltip>
  );
};

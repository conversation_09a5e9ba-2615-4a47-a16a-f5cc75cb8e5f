import { Link } from 'react-router-dom';
import { FavoriteButton } from '@/libs/products/components/FavoriteButton/FavoriteButton';
import { GpoRecommendedTag } from '@/libs/gpo/components/GpoRecommendedTag/GpoRecommendedTag';
import { SpecialInstructionIconList } from '../SpecialInstructionIconList/SpecialInstructionIconList';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { mergeClasses } from '@/utils';
import { OfferType } from '@/types';

type ProductImageProps = {
  product: ProductImageData;
  className?: string;
};

type ProductImageData = {
  id: string;
  name: string;
  imageUrl: string | null;
  offers: OfferType[];
  isFavorite?: boolean;
  isControlledSubstance?: boolean;
  isControlled222Form?: boolean;
  isHazardous?: boolean;
  requiresColdShipping?: boolean;
  requiresPedigree?: boolean;
  requiresPrescription?: boolean;
  productOfferId: string;
};

export const ProductImage = ({ product, className }: ProductImageProps) => {
  const {
    id,
    name,
    imageUrl,
    offers = [],
    isFavorite,
    isControlledSubstance = false,
    isControlled222Form = false,
    isHazardous = false,
    productOfferId,
    requiresColdShipping = false,
    requiresPedigree = false,
    requiresPrescription = false,
  } = product;
  const productUrl = getProductUrl(product.id, productOfferId);

  const offer = offers.find(({ id }) => id === productOfferId);

  const specialInstructions = {
    isControlledSubstance,
    isControlled222Form,
    isHazardous,
    requiresColdShipping,
    requiresPedigree,
    requiresPrescription,
  };

  return (
    <div
      className={mergeClasses(
        'relative h-36 w-40 overflow-hidden rounded-lg border border-gray-200 bg-white p-0',
        className,
      )}
    >
      {offer?.isRecommended !== undefined && (
        <GpoRecommendedTag
          isRecommended={offer.isRecommended}
          size="sm"
          top="0.75rem"
        />
      )}
      {isFavorite !== undefined && (
        <div className="absolute top-1 right-1">
          <FavoriteButton productId={id} isFavorite={isFavorite} />
        </div>
      )}
      <div className="absolute right-2 bottom-2">
        <SpecialInstructionIconList {...specialInstructions} />
      </div>
      <Link to={productUrl}>
        <img
          src={imageUrl || defaultProductImgUrl}
          alt={name}
          className={
            'absolute top-1/2 left-1/2 max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 object-scale-down'
          }
        />
      </Link>
    </div>
  );
};

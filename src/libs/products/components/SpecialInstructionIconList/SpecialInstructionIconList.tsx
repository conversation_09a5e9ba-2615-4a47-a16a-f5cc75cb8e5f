import { Flex } from '@/libs/ui/Flex/Flex';
import { SpecialInstructionIcon } from './SpecialInstructionIcon/SpecialInstructionIcon';
import { SpecialInstructionType } from './types';
import styles from './SpecialInstructionIconList.module.css';

type SpecialInstructionIconListProps = Record<SpecialInstructionType, boolean>;

export const SpecialInstructionIconList = ({
  isControlledSubstance,
  isControlled222Form,
  isHazardous,
  requiresColdShipping,
  requiresPedigree,
  requiresPrescription,
}: SpecialInstructionIconListProps) => {
  // Do not take isControlledSubstance into consideration if isControlled222Form is true
  const specialInstructionsObj = {
    isHazardous,
    requiresColdShipping,
    requiresPedigree,
    requiresPrescription,
    ...(isControlled222Form
      ? { isControlled222Form }
      : { isControlledSubstance }),
  };

  const specialInstructions = Object.keys(specialInstructionsObj).filter(
    (key): key is SpecialInstructionType =>
      Boolean(
        specialInstructionsObj[key as keyof typeof specialInstructionsObj],
      ),
  );

  return (
    <Flex gap="0.2rem" className={styles.container}>
      {specialInstructions.map((instruction) => (
        <SpecialInstructionIcon key={instruction} type={instruction} />
      ))}
    </Flex>
  );
};

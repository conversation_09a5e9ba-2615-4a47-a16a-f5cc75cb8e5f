import { useState } from 'react';
import type { ProductType } from '@/types';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { FavoriteButton } from '@/libs/products/components/FavoriteButton/FavoriteButton';
import { GpoRecommendedTag } from '@/libs/gpo/components/GpoRecommendedTag/GpoRecommendedTag';
import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { PurchaseHistory } from '@/libs/products/components/PurchaseHistory/PurchaseHistory';
import { SpecialInstructionIconList } from '@/libs/products/components/SpecialInstructionIconList/SpecialInstructionIconList';
import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { AddToCartForm } from '@/libs/products/components/AddToCartForm/AddToCartForm';
import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import { queryClient, queryKeys } from '@/libs/query/queryClient';
import { Price } from './components/Price';

export type ProductCardProps = {
  data: ProductType;
};

export const ProductCard = ({ data }: ProductCardProps) => {
  const { fetchProductDetails } = useProductStore();
  const { id, imageUrl, name, offers, isFavorite, manufacturer } = data;
  const [currentOfferId, setCurrentOfferId] = useState(offers[0].id);
  const currentOffer = offers.find((offer) => currentOfferId === offer.id);

  if (!currentOffer) {
    return null;
  }

  const {
    id: productOfferId,
    isRecommended,
    vendorSku,
    lastOrderedAt,
    lastOrderedQuantity,
    increments,
    unitOfMeasure,
    size,
  } = currentOffer;

  const productUrl = getProductUrl(id, productOfferId);

  return (
    <div className="relative flex h-[516px] flex-col rounded-lg border border-gray-200 bg-white p-4">
      <div className="relative mb-4 h-0 w-full overflow-hidden rounded-lg border border-gray-200 px-16 pb-[60%]">
        <GpoRecommendedTag isRecommended={isRecommended} top="0.75rem" />
        <Link
          to={productUrl}
          state={{ from: 'search' }}
          onMouseEnter={async () => {
            await queryClient.prefetchQuery({
              queryKey: queryKeys.products.details(id),
              queryFn: () => fetchProductDetails(id),
            });
          }}
        >
          <img
            src={imageUrl || defaultProductImgUrl}
            alt={name}
            className="absolute top-1/2 left-1/2 w-[70%] -translate-x-1/2 -translate-y-1/2 transform object-scale-down"
          />
        </Link>
        <div className="absolute right-4 bottom-4">
          <SpecialInstructionIconList {...data} />
        </div>

        <div className="absolute top-4 right-4">
          <FavoriteButton productId={id} isFavorite={isFavorite} />
        </div>
      </div>
      <Link
        to={productUrl}
        className="text-gray-800 no-underline hover:underline"
      >
        <h3 className="text-base leading-snug font-semibold">{name}</h3>
      </Link>
      <div className="mt-1 grid gap-3">
        <div className="flex text-xs">
          <p className="m-0 pr-3">
            <span className="text-gray-500">SKU:</span> {vendorSku}
          </p>
          {manufacturer ? (
            <p className="m-0 border-l border-gray-400 pl-3">{manufacturer}</p>
          ) : null}
        </div>
        <PurchaseHistory
          productId={currentOffer.id}
          lastOrderedAt={lastOrderedAt}
          lastOrderedQuantity={lastOrderedQuantity}
        />
        <VendorSwap
          currentOfferId={currentOffer.id}
          offers={offers}
          onSwap={setCurrentOfferId}
        />
      </div>

      <div className="flex flex-grow flex-col justify-end">
        {!!unitOfMeasure && (
          <span className="text-sxs text-gray-500">
            UOM: {`${size ? size + ' ' : ''} ${unitOfMeasure}`}
          </span>
        )}
        <Price offer={currentOffer} />
        <AddToCartForm
          disabled={currentOffer.isPurchasable === false}
          increments={increments}
          productOfferId={currentOfferId}
        />
      </div>
    </div>
  );
};

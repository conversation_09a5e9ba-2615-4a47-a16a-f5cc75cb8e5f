import { Button } from '@/libs/ui/Button/Button';
import ClipboardIcon from './assets/clipboard.svg?react';

interface DownloadChecklistProps {
  url: string;
  label: string;
  format: 'csv' | 'xlsx';
}
export const DownloadChecklist = ({
  url: originalUrl,
  label,
  format,
}: DownloadChecklistProps) => {
  const url = new URL(originalUrl);
  url.searchParams.set('export', format);

  return (
    <div className="gap-0.3rem flex flex-col">
      <div className="flex gap-2">
        <Button
          className="flex gap-2"
          variant="unstyled"
          href={url.toString()}
          download
        >
          <span className="flex items-center gap-1">
            <ClipboardIcon />
            <p className="text-xs font-medium text-[#4942D4] hover:underline">
              {label}
            </p>
          </span>
        </Button>
      </div>
    </div>
  );
};

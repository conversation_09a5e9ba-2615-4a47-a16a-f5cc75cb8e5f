import { Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import InvoiceIcon from './assets/invoice.svg?react';

interface DownloadInvoicesLinkProps {
  url: string;
}
export const DownloadInvoicesLink = ({ url }: DownloadInvoicesLinkProps) => (
  <Button
    variant="unstyled"
    href={url}
    style={{ marginBottom: '1rem' }}
    download
  >
    <div className="flex items-center gap-1">
      <InvoiceIcon />
      <Text c="#4942D4" size="xs" fw="500">
        Order Invoices
      </Text>
    </div>
  </Button>
);

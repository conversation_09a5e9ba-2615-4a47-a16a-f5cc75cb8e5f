import { CartItemType } from '@/libs/cart/types';
import { PromoType } from '@/types/common';
import { OfferType } from '@/types';
import {
  PromotionItemType,
  BuyXGetYPromotionData,
  ProcessedPromotionData,
} from '../types';
import { getMinimumQuantity } from './getMinimumQuantity';
import { getBenefitByType } from './getBenefitByType';

type PromotionType = Record<string, { items: CartItemType[] }> & {
  buy_x_get_y?: BuyXGetYPromotionData;
};

const getPromotionDetails = ({ item }: { item: CartItemType }) => {
  const promotions: PromoType[] = item.promotions || [];
  const promo = promotions.find((p) => p.type === 'buy_x_get_y');
  if (!promo) return null;

  const minimumQuantity = getMinimumQuantity(promo.requirements);
  const freeBenefit = getBenefitByType(promo.benefits, 'give_free_product');

  if (!freeBenefit) return null;

  const freeQtyPerTrigger = freeBenefit.quantity;
  const triggers = Math.floor(item.quantity / minimumQuantity);

  const freeItemsQty = triggers * freeQtyPerTrigger;

  const matchedOffer = item.product.offers.find(
    (offer) => offer.id === item.productOfferId,
  );
  const freeOffer = freeBenefit?.freeProductOffer ?? matchedOffer;

  return {
    promo,
    minimumQuantity,
    subtotalPaidItems: item.subtotal,
    subtotalAllItems:
      (item.quantity + freeItemsQty) * (Number(freeOffer?.price) * 1),
    freeItemsQty,
    freeOffer,
    manufacturer: item.product.manufacturer,
    imageUrl: item.product.imageUrl,
    wasTriggered: triggers > 0,
  };
};

const getBuyGetPromotionDetails = (items: CartItemType[]) => {
  return items.reduce(
    (acc, item) => {
      const info = getPromotionDetails({ item });
      if (!info || !info.freeOffer) return acc;

      return {
        subtotalPaidItems: acc.subtotalPaidItems + Number(item.subtotal || 0),
        subtotalAllItems: acc.subtotalAllItems + info.subtotalAllItems,
        paidItemsQty: acc.paidItemsQty + item.quantity,
        freeItemsQty: acc.freeItemsQty + info.freeItemsQty,
        promotion: info.promo,
        freeOffer: info.freeOffer,
        manufacturer: info.manufacturer || '',
        imageUrl: info.imageUrl || '',
        items: [
          ...acc.items,
          {
            ...item,
            freeItemsQty: info.freeItemsQty,
            freeOffer: info.freeOffer,
            minimumQuantity: info.minimumQuantity,
          },
        ],
      };
    },
    {
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
      promotion: null as PromoType | null,
      freeOffer: null as OfferType | null,
      manufacturer: '',
      imageUrl: '',
      items: [] as PromotionItemType[],
    },
  );
};

const hasTriggeredPromotion = (item: CartItemType): boolean => {
  if (!item.promotions || item.promotions.length === 0) {
    return false;
  }

  const promotionDetails = getPromotionDetails({ item });
  return Boolean(promotionDetails?.wasTriggered);
};

const groupTriggeredPromotionItems = (
  items: CartItemType[],
): Record<string, { items: CartItemType[] }> => {
  return items.reduce(
    (acc, item) => {
      if (hasTriggeredPromotion(item) && item.promotions) {
        item.promotions.forEach((promo) => {
          if (!acc[promo.type]) {
            acc[promo.type] = { items: [] };
          }
          acc[promo.type].items.push(item);
        });
      }
      return acc;
    },
    {} as Record<string, { items: CartItemType[] }>,
  );
};

export const getPromotionData = (
  items: CartItemType[],
): ProcessedPromotionData => {
  let promotions: PromotionType = groupTriggeredPromotionItems(items);

  if (promotions.buy_x_get_y) {
    promotions = {
      ...promotions,
      buy_x_get_y: {
        ...getBuyGetPromotionDetails(promotions.buy_x_get_y.items),
      },
    };
  }

  const nonPromotionItems = items.filter(
    (item) => !hasTriggeredPromotion(item),
  );

  return {
    promotions,
    nonPromotionItems,
  };
};

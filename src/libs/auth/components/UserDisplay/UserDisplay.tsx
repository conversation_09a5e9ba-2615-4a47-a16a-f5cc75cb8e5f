import { Avatar } from '@mantine/core';

interface User {
  name: string;
  email: string;
}

interface UserDisplayProps {
  user: User;
  UserSectionMenu: React.ComponentType;
}

export const UserDisplay = ({ user, UserSectionMenu }: UserDisplayProps) => {
  return (
    <div className="flex min-w-[224px] items-center rounded-[.25rem] border border-[#E8E8E9] bg-white p-[.75rem]">
      <div className="mr-2">
        <Avatar radius="xl" />
      </div>
      <div className="flex-grow overflow-hidden text-ellipsis whitespace-nowrap">
        <p className="text-xs leading-5 font-medium text-[#161924]">
          {user.name}
        </p>
        <p className="text-xs leading-4 text-[#5A5C66]">{user.email}</p>
      </div>
      <UserSectionMenu />
    </div>
  );
};
